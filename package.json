{"name": "test-tempo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "react-native": {"tempo-devtools": "./utils/tempo-devtools-mock.ts"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "autoprefixer": "^10.4.20", "expo": "^53.0.0", "expo-atlas": "^0.4.0", "expo-blur": "~14.1.4", "expo-camera": "~16.1.8", "expo-constants": "~17.1.5", "expo-crypto": "^14.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.4", "expo-media-library": "~17.1.7", "expo-router": "~5.0.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lightningcss": "^1.29.2", "lucide-react-native": "^0.479.0", "nativewind": "^4.1.23", "postcss": "^8.5.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~53.0.4", "react-test-renderer": "18.3.1", "tempo-devtools": "^2.0.108", "typescript": "~5.8.3"}, "private": true}
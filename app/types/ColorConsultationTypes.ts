// Tipos y interfaces para el módulo de consulta de color ultra-inteligente
// Versión mejorada según requisitos 4.2.1 - 4.2.5

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  image?: string;
  lastVisit?: string;
  isGuest?: boolean; // Para clientes invitados
  consentGiven?: boolean; // Consentimiento para imágenes y datos
  consentTimestamp?: string;
}

export interface ConsentData {
  imageCapture: boolean;
  dataAnalysis: boolean;
  dataStorage: boolean;
  aiProcessing: boolean;
  timestamp: string;
  clientSignature?: string;
  stylistWitness: string;
}

export interface ImageMetadata {
  id: string;
  uri: string;
  zone: 'frontal' | 'coronilla' | 'lateral_izquierdo' | 'lateral_derecho' | 'nuca' | 'detalle';
  timestamp: string;
  quality: 'excellent' | 'good' | 'acceptable' | 'poor';
  lightingConditions: 'natural' | 'artificial_good' | 'artificial_poor' | 'mixed';
  faceBlurred: boolean;
  originalSize: number;
  compressedSize: number;
}

export interface HairDiagnosisData {
  // Análisis por zonas
  roots: {
    naturalLevel: number; // 1-10
    undertone: 'warm' | 'cool' | 'neutral';
    grayPercentage: number; // 0-100
    regrowth: number; // mm
    condition: 'healthy' | 'normal' | 'damaged' | 'severely_damaged';
  };
  midLengths: {
    level: number;
    undertone: 'warm' | 'cool' | 'neutral';
    porosity: 'low' | 'medium' | 'high';
    elasticity: 'excellent' | 'good' | 'poor';
    condition: 'healthy' | 'normal' | 'damaged' | 'severely_damaged';
  };
  ends: {
    level: number;
    porosity: 'low' | 'medium' | 'high';
    condition: 'healthy' | 'normal' | 'damaged' | 'severely_damaged';
    needsTrimming: boolean;
  };
  
  // Análisis general
  hairDiameter: 'fine' | 'medium' | 'coarse';
  density: 'low' | 'medium' | 'high';
  texture: 'straight' | 'wavy' | 'curly' | 'coily';
  resistance: 'low' | 'medium' | 'high';
  
  // Historial químico detallado
  chemicalHistory: Array<{
    treatment: string;
    date: string;
    products: string[];
    result: 'excellent' | 'good' | 'poor' | 'damaged';
    notes?: string;
  }>;
  
  // Información del cliente (obligatoria)
  clientInfo: {
    allergies: string[];
    sensitivities: string[];
    medications: string[];
    scalpCondition: 'healthy' | 'sensitive' | 'irritated' | 'psoriasis' | 'eczema' | 'other';
    pregnancyStatus: 'not_pregnant' | 'pregnant' | 'breastfeeding' | 'unknown';
    previousReactions: string[];
    lifestyle: {
      washFrequency: 'daily' | 'every_other_day' | 'twice_weekly' | 'weekly' | 'less_than_weekly';
      heatStyling: 'daily' | 'frequent' | 'occasional' | 'never';
      swimmingExposure: 'frequent' | 'occasional' | 'never';
      sunExposure: 'high' | 'moderate' | 'low';
    };
  };
  
  // Metadatos del análisis
  images: ImageMetadata[];
  aiAnalysis: {
    confidence: number; // 0-100
    processingTime: number; // segundos
    modelVersion: string;
    analysisNotes: string[];
  };
  stylistValidation: {
    reviewed: boolean;
    modifications: string[];
    finalApproval: boolean;
    reviewTimestamp?: string;
  };
}

export interface DesiredColorData {
  // Análisis de imágenes de referencia
  referenceImages: Array<{
    id: string;
    uri: string;
    analysis: {
      level: number;
      undertone: 'warm' | 'cool' | 'neutral';
      reflections: string[];
      technique: string;
      luminosity: number;
      saturation: number;
    };
    faceBlurred: boolean;
  }>;
  
  // Definición del objetivo
  targetLevel: number;
  targetUndertone: 'warm' | 'cool' | 'neutral';
  targetReflections: string[];
  technique: 'global' | 'highlights' | 'lowlights' | 'balayage' | 'ombre' | 'color_melting' | 'fantasy';
  intensity: 'subtle' | 'medium' | 'dramatic';
  
  // Descripción verbal del estilista
  stylistDescription: string;
  clientPreferences: string;
  
  // Validación
  stylistApproval: boolean;
  clientApproval: boolean;
}

export interface FormulationData {
  // Entradas para la IA
  diagnosis: HairDiagnosisData;
  desiredColor: DesiredColorData;
  preferredBrands: string[];
  availableProducts?: string[];
  
  // Análisis de viabilidad por IA
  viabilityAnalysis: {
    achievable: boolean;
    sessionsRequired: number;
    riskLevel: 'low' | 'medium' | 'high' | 'very_high';
    lightening: number; // niveles
    darkening: number; // niveles
    warnings: string[];
    recommendations: string[];
    pretreatments: string[];
    posttreatments: string[];
  };
  
  // Fórmula generada por IA
  aiFormula: {
    products: Array<{
      brand: string;
      line: string;
      name: string;
      code: string;
      amount: string;
      purpose: 'base' | 'toner' | 'additive' | 'developer';
    }>;
    developer: {
      volume: number;
      ratio: string;
      brand: string;
    };
    processingTime: number;
    applicationTechnique: string;
    stepByStepInstructions: string[];
    estimatedCost: number;
    confidence: number; // 0-100
  };
  
  // Fórmula final del estilista
  finalFormula: {
    products: Array<{
      brand: string;
      line: string;
      name: string;
      code: string;
      amount: string;
      purpose: 'base' | 'toner' | 'additive' | 'developer';
    }>;
    developer: {
      volume: number;
      ratio: string;
      brand: string;
    };
    processingTime: number;
    applicationTechnique: string;
    instructions: string[];
    modifications: string[]; // Cambios respecto a la IA
    estimatedCost: number;
  };
  
  // Control del estilista
  stylistOverrides: string[];
  finalApproval: boolean;
}

export interface ServiceDocumentation {
  // Fotos del resultado
  resultImages: Array<{
    id: string;
    uri: string;
    zone: 'frontal' | 'lateral' | 'posterior' | 'detalle';
    timestamp: string;
    faceBlurred: boolean;
  }>;
  
  // Fórmula aplicada (puede diferir de la planificada)
  appliedFormula: {
    products: Array<{
      brand: string;
      name: string;
      code: string;
      amount: string;
    }>;
    developer: {
      volume: number;
      ratio: string;
    };
    actualProcessingTime: number;
    modifications: string[];
  };
  
  // Notas del servicio
  stylistNotes: string;
  clientFeedback: string;
  clientSatisfaction: 1 | 2 | 3 | 4 | 5;
  
  // Recomendaciones
  maintenanceInstructions: string;
  nextAppointmentRecommendation: string;
  productRecommendations: string[];
  
  // Metadatos
  serviceDate: string;
  duration: number; // minutos
  totalCost: number;
}

export interface ColorConsultationData {
  id: string;
  client: Client;
  consent: ConsentData;
  diagnosis: HairDiagnosisData;
  desiredColor: DesiredColorData;
  formulation: FormulationData;
  documentation?: ServiceDocumentation;
  
  // Estado del proceso
  currentPhase: 'client_selection' | 'diagnosis' | 'color_definition' | 'formulation' | 'documentation' | 'completed';
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled';
  
  // Metadatos
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  stylistId: string;
  appointmentId?: string;
}

// Configuración para optimización de costes
export interface CostOptimizationConfig {
  maxImagesPerPhase: {
    diagnosis: number;
    reference: number;
    result: number;
  };
  imageCompression: {
    quality: number;
    maxWidth: number;
    maxHeight: number;
  };
  aiPrompts: {
    diagnosis: string;
    colorAnalysis: string;
    formulation: string;
  };
  modelSelection: {
    primary: string;
    fallback: string;
    economical: string;
  };
}

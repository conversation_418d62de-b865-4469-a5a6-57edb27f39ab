import React from "react";
import { View, Text, ScrollView, TouchableOpacity, Image } from "react-native";
import { StatusBar } from "expo-status-bar";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import {
  User,
  Calendar,
  Users,
  BarChart3,
  Bell,
  Settings,
  Zap,
} from "lucide-react-native";

export default function HomeScreen() {
  const router = useRouter();
  const stylistName = "María"; // Placeholder name

  // Mock data for action cards
  const actionCards = [
    {
      id: 1,
      title: "Gestión de Clientes",
      description: "Busca, añade y gestiona tus clientes",
      icon: <Users size={24} color="#fff" />,
      color: "#4f46e5",
      route: "/components/ClientManagement",
    },
    {
      id: 2,
      title: "Formulación Express",
      description: "🎨 Fórmula profesional en 3 pasos",
      icon: <Zap size={24} color="#fff" />,
      color: "#8b5cf6",
      route: "/components/ProfessionalColorFormulationV2",
    },
    {
      id: 3,
      title: "Consulta de Color",
      description: "Consulta tradicional completa",
      icon: <User size={24} color="#fff" />,
      color: "#ec4899",
      route: "/components/ColorConsultation",
    },
    {
      id: 4,
      title: "Calendario",
      description: "Gestiona tus citas y horarios",
      icon: <Calendar size={24} color="#fff" />,
      color: "#0ea5e9",
      route: "/components/AppointmentCalendar",
    },
    {
      id: 5,
      title: "Analíticas",
      description: "Visualiza el rendimiento de tu negocio",
      icon: <BarChart3 size={24} color="#fff" />,
      color: "#10b981",
      route: "/analytics",
    },
  ];

  // Mock data for upcoming appointments
  const upcomingAppointments = [
    {
      id: 1,
      clientName: "Laura Martínez",
      service: "Balayage + Corte",
      time: "10:00 AM",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Laura",
    },
    {
      id: 2,
      clientName: "Carlos Ruiz",
      service: "Color Global + Tratamiento",
      time: "12:30 PM",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Carlos",
    },
    {
      id: 3,
      clientName: "Ana García",
      service: "Mechas + Peinado",
      time: "3:15 PM",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      {/* Header */}
      <View className="flex-row justify-between items-center px-4 py-3 border-b border-gray-200">
        <View className="flex-row items-center">
          <Text className="text-2xl font-bold text-indigo-600">Salonier</Text>
        </View>
        <View className="flex-row items-center space-x-4">
          <TouchableOpacity>
            <Bell size={24} color="#4b5563" />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => router.push("/components/Settings")}>
            <Settings size={24} color="#4b5563" />
          </TouchableOpacity>
          <TouchableOpacity className="h-8 w-8 rounded-full bg-indigo-100 items-center justify-center">
            <Text className="text-indigo-600 font-bold">M</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View className="px-4 py-6">
          <Text className="text-2xl font-bold text-gray-800">
            ¡Hola, {stylistName}!
          </Text>
          <Text className="text-gray-600 mt-1">¿Qué quieres hacer hoy?</Text>
        </View>

        {/* Action Cards */}
        <View className="px-4 pb-6">
          <View className="flex-row flex-wrap justify-between">
            {actionCards.slice(0, 4).map((card) => (
              <TouchableOpacity
                key={card.id}
                style={{ backgroundColor: card.color }}
                className="w-[48%] rounded-xl p-4 mb-4"
                onPress={() => router.push(card.route)}
              >
                <View className="h-10 w-10 rounded-full bg-white/20 items-center justify-center mb-3">
                  {card.icon}
                </View>
                <Text className="text-white font-bold text-lg">
                  {card.title}
                </Text>
                <Text className="text-white/80 text-sm mt-1">
                  {card.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Quinta tarjeta centrada */}
          {actionCards.length > 4 && (
            <View className="items-center">
              <TouchableOpacity
                style={{ backgroundColor: actionCards[4].color }}
                className="w-[48%] rounded-xl p-4"
                onPress={() => router.push(actionCards[4].route)}
              >
                <View className="h-10 w-10 rounded-full bg-white/20 items-center justify-center mb-3">
                  {actionCards[4].icon}
                </View>
                <Text className="text-white font-bold text-lg">
                  {actionCards[4].title}
                </Text>
                <Text className="text-white/80 text-sm mt-1">
                  {actionCards[4].description}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Today's Appointments */}
        <View className="px-4 pb-8">
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-bold text-gray-800">
              Citas de Hoy
            </Text>
            <TouchableOpacity>
              <Text className="text-indigo-600">Ver todas</Text>
            </TouchableOpacity>
          </View>

          {upcomingAppointments.map((appointment) => (
            <TouchableOpacity
              key={appointment.id}
              className="flex-row items-center bg-gray-50 rounded-lg p-3 mb-3"
            >
              <Image
                source={{ uri: appointment.avatar }}
                className="h-12 w-12 rounded-full"
              />
              <View className="ml-3 flex-1">
                <Text className="font-bold text-gray-800">
                  {appointment.clientName}
                </Text>
                <Text className="text-gray-600">{appointment.service}</Text>
              </View>
              <View className="bg-indigo-100 px-3 py-1 rounded-full">
                <Text className="text-indigo-600 font-medium">
                  {appointment.time}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Quick Actions */}
        <View className="px-4 pb-8">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            Acciones Rápidas
          </Text>
          <View className="flex-row justify-between">
            <TouchableOpacity
              className="items-center w-[23%]"
              onPress={() => router.push("/components/ClientManagement")}
            >
              <View className="h-14 w-14 rounded-full bg-purple-100 items-center justify-center mb-2">
                <User size={24} color="#7e22ce" />
              </View>
              <Text className="text-center text-gray-700 text-xs">
                Nuevo Cliente
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="items-center w-[23%]"
              onPress={() => router.push("/components/AppointmentCalendar")}
            >
              <View className="h-14 w-14 rounded-full bg-pink-100 items-center justify-center mb-2">
                <Calendar size={24} color="#db2777" />
              </View>
              <Text className="text-center text-gray-700 text-xs">
                Nueva Cita
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="items-center w-[23%]"
              onPress={() => router.push("/components/ColorConsultation")}
            >
              <View className="h-14 w-14 rounded-full bg-blue-100 items-center justify-center mb-2">
                <Users size={24} color="#2563eb" />
              </View>
              <Text className="text-center text-gray-700 text-xs">
                Consulta
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="items-center w-[23%]"
              onPress={() => router.push("/analytics")}
            >
              <View className="h-14 w-14 rounded-full bg-green-100 items-center justify-center mb-2">
                <BarChart3 size={24} color="#16a34a" />
              </View>
              <Text className="text-center text-gray-700 text-xs">
                Reportes
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

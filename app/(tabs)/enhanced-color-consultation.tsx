import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
} from "react-native";
import {
  ArrowLeft,
  Sparkles,
  Shield,
  Zap,
  Camera,
  Brain,
  CheckCircle,
  Star,
  Clock,
  Users,
  TrendingUp,
  Award,
  Info,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import EnhancedColorConsultation from "../components/enhanced/EnhancedColorConsultation";
import PrivacyManager from "../utils/PrivacySecurity";
import { CostOptimizer } from "../utils/CostOptimization";

export default function EnhancedColorConsultationScreen() {
  const router = useRouter();
  const [showDemo, setShowDemo] = useState(false);
  const [privacyCompliance, setPrivacyCompliance] = useState<any>(null);
  const [costOptimizer] = useState(new CostOptimizer());

  useEffect(() => {
    checkPrivacyCompliance();
  }, []);

  const checkPrivacyCompliance = async () => {
    try {
      const privacyManager = PrivacyManager.getInstance();
      const compliance = await privacyManager.validateGDPRCompliance();
      setPrivacyCompliance(compliance);
    } catch (error) {
      console.error('Error verificando cumplimiento de privacidad:', error);
    }
  };

  const handleStartDemo = () => {
    if (privacyCompliance && !privacyCompliance.compliant) {
      Alert.alert(
        'Configuración de Privacidad',
        'Se detectaron problemas de cumplimiento de privacidad. ¿Deseas continuar?',
        [
          { text: 'Cancelar', style: 'cancel' },
          { text: 'Continuar', onPress: () => setShowDemo(true) },
        ]
      );
    } else {
      setShowDemo(true);
    }
  };

  const handleBackToMenu = () => {
    setShowDemo(false);
    costOptimizer.resetStats();
  };

  if (showDemo) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
        
        {/* Header del demo */}
        <View className="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
          <View className="flex-row items-center justify-between">
            <TouchableOpacity
              onPress={handleBackToMenu}
              className="flex-row items-center"
            >
              <ArrowLeft size={24} color="#374151" />
              <Text className="text-gray-800 font-semibold ml-2">Volver</Text>
            </TouchableOpacity>
            
            <View className="bg-purple-100 px-3 py-1 rounded-full">
              <Text className="text-purple-800 font-semibold text-sm">
                🚀 CONSULTA ULTRA-INTELIGENTE
              </Text>
            </View>
          </View>
        </View>

        {/* Componente principal */}
        <EnhancedColorConsultation
          onComplete={(consultation) => {
            console.log('Consulta completada:', consultation);
            handleBackToMenu();
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
      
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-8">
          <View className="flex-row items-center mb-4">
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
            <Text className="text-white text-xl font-bold ml-4">
              Consulta de Color Ultra-Inteligente
            </Text>
          </View>
          
          <Text className="text-purple-100 text-lg mb-2">
            Powered by AI • Fase 4.2 Completa
          </Text>
          <Text className="text-purple-50">
            Flujo completo de consulta con IA avanzada, privacidad garantizada y control total del estilista
          </Text>
        </View>

        {/* Características principales */}
        <View className="px-6 py-6">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            Características Avanzadas
          </Text>
          
          <View className="space-y-4">
            <View className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
              <View className="flex-row items-center mb-2">
                <Users size={20} color="#8B5CF6" />
                <Text className="text-purple-800 font-semibold ml-2">
                  Selección de Cliente y Consentimiento (4.2.1)
                </Text>
              </View>
              <Text className="text-gray-600 text-sm">
                • Búsqueda avanzada de clientes con opción de invitado{'\n'}
                • Consentimiento informado para captura de imágenes{'\n'}
                • Cumplimiento GDPR/LOPD automático
              </Text>
            </View>

            <View className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
              <View className="flex-row items-center mb-2">
                <Camera size={20} color="#10B981" />
                <Text className="text-green-800 font-semibold ml-2">
                  Diagnóstico Capilar Exhaustivo (4.2.2)
                </Text>
              </View>
              <Text className="text-gray-600 text-sm">
                • Captura multi-imagen (3-7 fotos) con guías interactivas{'\n'}
                • Difuminado automático de rostros para privacidad{'\n'}
                • Análisis IA por zonas: raíces, medios, puntas{'\n'}
                • Validación y edición por el estilista
              </Text>
            </View>

            <View className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
              <View className="flex-row items-center mb-2">
                <Sparkles size={20} color="#F59E0B" />
                <Text className="text-amber-800 font-semibold ml-2">
                  Definición Precisa del Color (4.2.3)
                </Text>
              </View>
              <Text className="text-gray-600 text-sm">
                • Análisis IA de imágenes de referencia{'\n'}
                • Herramientas de selección con paletas digitales{'\n'}
                • Comparación automática con diagnóstico actual{'\n'}
                • Control total del estilista en definición final
              </Text>
            </View>

            <View className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
              <View className="flex-row items-center mb-2">
                <Brain size={20} color="#EF4444" />
                <Text className="text-red-800 font-semibold ml-2">
                  Formulación Experta con IA (4.2.4)
                </Text>
              </View>
              <Text className="text-gray-600 text-sm">
                • Análisis de viabilidad y riesgo automático{'\n'}
                • Fórmula detallada con marcas preferidas{'\n'}
                • Instrucciones paso a paso personalizadas{'\n'}
                • Edición completa por el estilista profesional
              </Text>
            </View>

            <View className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
              <View className="flex-row items-center mb-2">
                <CheckCircle size={20} color="#3B82F6" />
                <Text className="text-blue-800 font-semibold ml-2">
                  Documentación Exhaustiva (4.2.5)
                </Text>
              </View>
              <Text className="text-gray-600 text-sm">
                • Fotos del resultado con rostros difuminados{'\n'}
                • Registro de fórmula aplicada vs. planificada{'\n'}
                • Notas del estilista y valoración del cliente{'\n'}
                • Vinculación automática al historial
              </Text>
            </View>
          </View>
        </View>

        {/* Optimizaciones */}
        <View className="px-6 py-4">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            Optimizaciones Integradas
          </Text>
          
          <View className="grid grid-cols-2 gap-4">
            <View className="bg-green-50 p-4 rounded-lg border border-green-200">
              <View className="flex-row items-center mb-2">
                <Zap size={18} color="#10B981" />
                <Text className="text-green-800 font-medium ml-2">Costes IA</Text>
              </View>
              <Text className="text-green-700 text-sm">
                Prompts optimizados, compresión inteligente, selección de modelo
              </Text>
            </View>
            
            <View className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <View className="flex-row items-center mb-2">
                <Shield size={18} color="#3B82F6" />
                <Text className="text-blue-800 font-medium ml-2">Privacidad</Text>
              </View>
              <Text className="text-blue-700 text-sm">
                Difuminado automático, encriptación, cumplimiento GDPR
              </Text>
            </View>
            
            <View className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <View className="flex-row items-center mb-2">
                <TrendingUp size={18} color="#8B5CF6" />
                <Text className="text-purple-800 font-medium ml-2">UX Avanzada</Text>
              </View>
              <Text className="text-purple-700 text-sm">
                Guías contextuales, feedback en tiempo real, progreso dinámico
              </Text>
            </View>
            
            <View className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <View className="flex-row items-center mb-2">
                <Award size={18} color="#F97316" />
                <Text className="text-orange-800 font-medium ml-2">Profesional</Text>
              </View>
              <Text className="text-orange-700 text-sm">
                Control total del estilista, validación experta, edición completa
              </Text>
            </View>
          </View>
        </View>

        {/* Estado de privacidad */}
        {privacyCompliance && (
          <View className="px-6 py-4">
            <View className={`p-4 rounded-lg border ${
              privacyCompliance.compliant 
                ? 'bg-green-50 border-green-200' 
                : 'bg-yellow-50 border-yellow-200'
            }`}>
              <View className="flex-row items-center mb-2">
                <Shield size={18} color={privacyCompliance.compliant ? "#10B981" : "#F59E0B"} />
                <Text className={`font-medium ml-2 ${
                  privacyCompliance.compliant ? 'text-green-800' : 'text-yellow-800'
                }`}>
                  Estado de Privacidad: {privacyCompliance.compliant ? 'Cumple' : 'Requiere Atención'}
                </Text>
              </View>
              {!privacyCompliance.compliant && privacyCompliance.issues.length > 0 && (
                <Text className="text-yellow-700 text-sm">
                  Problemas: {privacyCompliance.issues.join(', ')}
                </Text>
              )}
            </View>
          </View>
        )}

        {/* Estadísticas de rendimiento */}
        <View className="px-6 py-4">
          <Text className="text-xl font-bold text-gray-800 mb-4">
            Rendimiento Esperado
          </Text>
          
          <View className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
            <View className="grid grid-cols-3 gap-4">
              <View className="items-center">
                <Clock size={24} color="#6B7280" />
                <Text className="text-gray-800 font-semibold mt-2">3-5 min</Text>
                <Text className="text-gray-600 text-sm text-center">Tiempo total</Text>
              </View>
              <View className="items-center">
                <Star size={24} color="#6B7280" />
                <Text className="text-gray-800 font-semibold mt-2">85-95%</Text>
                <Text className="text-gray-600 text-sm text-center">Precisión IA</Text>
              </View>
              <View className="items-center">
                <TrendingUp size={24} color="#6B7280" />
                <Text className="text-gray-800 font-semibold mt-2">€2-5</Text>
                <Text className="text-gray-600 text-sm text-center">Coste IA</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Información adicional */}
        <View className="px-6 py-4">
          <View className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <View className="flex-row items-center mb-2">
              <Info size={18} color="#3B82F6" />
              <Text className="text-blue-800 font-medium ml-2">Información Importante</Text>
            </View>
            <Text className="text-blue-700 text-sm">
              Este módulo implementa todos los requisitos especificados en la fase 4.2. 
              Incluye optimizaciones de costes, cumplimiento de privacidad y control total del estilista profesional.
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Botón de inicio */}
      <View className="bg-white p-4 border-t border-gray-100">
        <TouchableOpacity
          className="bg-gradient-to-r from-purple-500 to-blue-500 py-4 rounded-lg shadow-lg"
          onPress={handleStartDemo}
        >
          <View className="flex-row items-center justify-center">
            <Sparkles size={24} color="white" />
            <Text className="text-white font-bold text-lg ml-2">
              Iniciar Consulta Ultra-Inteligente
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  TextInput,
} from "react-native";
import {
  FlaskConical,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Shield,
  Zap,
  Edit3,
  Star,
  TrendingUp,
  Target,
  Beaker,
} from "lucide-react-native";
import { FormulationData, HairDiagnosisData, DesiredColorData } from "../../types/ColorConsultationTypes";

interface ExpertFormulationProps {
  diagnosis: HairDiagnosisData;
  desiredColor: DesiredColorData;
  onFormulationComplete: (formulation: FormulationData) => void;
  onBack: () => void;
}

const ExpertFormulation: React.FC<ExpertFormulationProps> = ({
  diagnosis,
  desiredColor,
  onFormulationComplete,
  onBack,
}) => {
  const [currentStep, setCurrentStep] = useState<'brands' | 'analysis' | 'formulation' | 'validation'>('brands');
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [availableProducts, setAvailableProducts] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [viabilityAnalysis, setViabilityAnalysis] = useState<FormulationData['viabilityAnalysis'] | null>(null);
  const [aiFormula, setAiFormula] = useState<FormulationData['aiFormula'] | null>(null);
  const [finalFormula, setFinalFormula] = useState<FormulationData['finalFormula'] | null>(null);
  const [stylistOverrides, setStylistOverrides] = useState<string[]>([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<any>(null);

  const popularBrands = [
    { id: 'loreal', name: 'L\'Oréal Professionnel', lines: ['INOA', 'Majirel', 'DiaRichesse'] },
    { id: 'wella', name: 'Wella Professionals', lines: ['Koleston Perfect', 'Color Touch', 'Illumina'] },
    { id: 'schwarzkopf', name: 'Schwarzkopf Professional', lines: ['Igora Royal', 'BlondMe', 'Color10'] },
    { id: 'matrix', name: 'Matrix', lines: ['SoColor', 'Color Sync', 'Light Master'] },
    { id: 'redken', name: 'Redken', lines: ['Chromatics', 'Shades EQ', 'Color Fusion'] },
    { id: 'goldwell', name: 'Goldwell', lines: ['Topchic', 'Colorance', 'Elumen'] },
  ];

  useEffect(() => {
    if (selectedBrands.length > 0 && currentStep === 'brands') {
      setCurrentStep('analysis');
      performViabilityAnalysis();
    }
  }, [selectedBrands]);

  const performViabilityAnalysis = async () => {
    setIsAnalyzing(true);

    try {
      // Simular análisis de viabilidad por IA
      await new Promise(resolve => setTimeout(resolve, 3000));

      const currentLevel = diagnosis.roots.naturalLevel;
      const targetLevel = desiredColor.targetLevel;
      const levelDifference = Math.abs(targetLevel - currentLevel);
      
      const analysis: FormulationData['viabilityAnalysis'] = {
        achievable: levelDifference <= 4 || diagnosis.roots.condition !== 'severely_damaged',
        sessionsRequired: levelDifference > 3 ? 2 : 1,
        riskLevel: levelDifference > 4 ? 'high' : levelDifference > 2 ? 'medium' : 'low',
        lightening: Math.max(0, targetLevel - currentLevel),
        darkening: Math.max(0, currentLevel - targetLevel),
        warnings: generateWarnings(diagnosis, desiredColor, levelDifference),
        recommendations: generateRecommendations(diagnosis, desiredColor, levelDifference),
        pretreatments: generatePretreatments(diagnosis, levelDifference),
        posttreatments: generatePosttreatments(diagnosis, levelDifference),
      };

      setViabilityAnalysis(analysis);
      setCurrentStep('formulation');
      
      // Generar fórmula IA
      await generateAIFormula(analysis);

    } catch (error) {
      console.error('Error en análisis de viabilidad:', error);
      Alert.alert('Error', 'Error durante el análisis de viabilidad.');
      setCurrentStep('brands');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateWarnings = (diagnosis: HairDiagnosisData, desired: DesiredColorData, levelDiff: number): string[] => {
    const warnings = [];
    
    if (levelDiff > 4) {
      warnings.push('Cambio de más de 4 niveles requiere decoloración previa');
    }
    
    if (diagnosis.roots.condition === 'damaged' || diagnosis.ends.condition === 'damaged') {
      warnings.push('Cabello dañado - considerar tratamiento reconstructor previo');
    }
    
    if (diagnosis.roots.grayPercentage > 50) {
      warnings.push('Alto porcentaje de canas - ajustar tiempo de pose y productos');
    }
    
    if (diagnosis.midLengths.porosity === 'high') {
      warnings.push('Alta porosidad - riesgo de absorción desigual del color');
    }

    return warnings;
  };

  const generateRecommendations = (diagnosis: HairDiagnosisData, desired: DesiredColorData, levelDiff: number): string[] => {
    const recommendations = [];
    
    if (levelDiff > 2) {
      recommendations.push('Realizar prueba de mecha antes del servicio completo');
    }
    
    if (diagnosis.ends.needsTrimming) {
      recommendations.push('Cortar puntas antes de la coloración para mejor resultado');
    }
    
    recommendations.push('Aplicar tratamiento hidratante post-coloración');
    recommendations.push('Programar retoque en 6-8 semanas');
    
    return recommendations;
  };

  const generatePretreatments = (diagnosis: HairDiagnosisData, levelDiff: number): string[] => {
    const pretreatments = [];
    
    if (diagnosis.roots.condition === 'damaged') {
      pretreatments.push('Tratamiento reconstructor con proteínas');
    }
    
    if (diagnosis.midLengths.porosity === 'high') {
      pretreatments.push('Relleno de porosidad con pH ácido');
    }
    
    if (levelDiff > 3) {
      pretreatments.push('Pre-decoloración con protector capilar');
    }
    
    return pretreatments;
  };

  const generatePosttreatments = (diagnosis: HairDiagnosisData, levelDiff: number): string[] => {
    const posttreatments = [];
    
    posttreatments.push('Tratamiento hidratante intensivo');
    posttreatments.push('Sellador de cutícula con pH ácido');
    
    if (levelDiff > 2) {
      posttreatments.push('Mascarilla reconstructora semanal');
    }
    
    return posttreatments;
  };

  const generateAIFormula = async (analysis: FormulationData['viabilityAnalysis']) => {
    try {
      // Simular generación de fórmula por IA
      await new Promise(resolve => setTimeout(resolve, 2000));

      const primaryBrand = selectedBrands[0];
      const brandData = popularBrands.find(b => b.id === primaryBrand);
      
      const formula: FormulationData['aiFormula'] = {
        products: [
          {
            brand: brandData?.name || 'L\'Oréal Professionnel',
            line: brandData?.lines[0] || 'INOA',
            name: 'Colorante Base',
            code: `${desiredColor.targetLevel}.0`,
            amount: '60ml',
            purpose: 'base',
          },
          {
            brand: brandData?.name || 'L\'Oréal Professionnel',
            line: brandData?.lines[0] || 'INOA',
            name: 'Matizador',
            code: `${desiredColor.targetLevel}.${desiredColor.targetUndertone === 'warm' ? '3' : '1'}`,
            amount: '15ml',
            purpose: 'toner',
          },
        ],
        developer: {
          volume: analysis.lightening > 2 ? 30 : 20,
          ratio: '1:1',
          brand: brandData?.name || 'L\'Oréal Professionnel',
        },
        processingTime: analysis.lightening > 2 ? 45 : 35,
        applicationTechnique: desiredColor.technique === 'global' ? 'Aplicación global' : 'Técnica selectiva',
        stepByStepInstructions: [
          'Realizar prueba de sensibilidad 48h antes',
          'Aplicar producto protector en cuero cabelludo',
          'Mezclar productos según proporción indicada',
          'Aplicar desde medios a puntas, luego raíces',
          'Controlar evolución cada 10 minutos',
          'Enjuagar abundantemente con agua tibia',
          'Aplicar tratamiento post-coloración',
        ],
        estimatedCost: 45 + (analysis.sessionsRequired - 1) * 35,
        confidence: 88 + Math.random() * 8,
      };

      setAiFormula(formula);
      setFinalFormula({
        products: [...formula.products],
        developer: { ...formula.developer },
        processingTime: formula.processingTime,
        applicationTechnique: formula.applicationTechnique,
        instructions: [...formula.stepByStepInstructions],
        modifications: [],
        estimatedCost: formula.estimatedCost,
      });

      Alert.alert(
        'Fórmula Generada',
        `Fórmula IA generada con ${Math.round(formula.confidence)}% de confianza.\n\nRevisa y modifica según tu criterio profesional.`
      );

    } catch (error) {
      console.error('Error generando fórmula:', error);
      Alert.alert('Error', 'Error generando la fórmula IA.');
    }
  };

  const handleBrandSelection = (brandId: string) => {
    if (selectedBrands.includes(brandId)) {
      setSelectedBrands(selectedBrands.filter(id => id !== brandId));
    } else {
      setSelectedBrands([...selectedBrands, brandId]);
    }
  };

  const handleProductEdit = (productIndex: number) => {
    if (!finalFormula) return;
    setEditingProduct({ ...finalFormula.products[productIndex], index: productIndex });
    setShowEditModal(true);
  };

  const saveProductEdit = () => {
    if (!finalFormula || !editingProduct) return;

    const updatedProducts = [...finalFormula.products];
    updatedProducts[editingProduct.index] = {
      brand: editingProduct.brand,
      line: editingProduct.line,
      name: editingProduct.name,
      code: editingProduct.code,
      amount: editingProduct.amount,
      purpose: editingProduct.purpose,
    };

    setFinalFormula({
      ...finalFormula,
      products: updatedProducts,
      modifications: [...finalFormula.modifications, `Modificado producto ${editingProduct.index + 1}`],
    });

    setStylistOverrides([...stylistOverrides, `Producto modificado: ${editingProduct.name}`]);
    setShowEditModal(false);
    setEditingProduct(null);
  };

  const handleFormulationComplete = () => {
    if (!viabilityAnalysis || !aiFormula || !finalFormula) return;

    const formulation: FormulationData = {
      diagnosis,
      desiredColor,
      preferredBrands: selectedBrands,
      availableProducts,
      viabilityAnalysis,
      aiFormula,
      finalFormula,
      stylistOverrides,
      finalApproval: true,
    };

    onFormulationComplete(formulation);
  };

  const renderBrandsStep = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Marcas y Líneas Preferidas</Text>
        <Text className="text-gray-600 mb-4">
          Selecciona las marcas que tienes disponibles en tu salón
        </Text>
      </View>

      <View className="space-y-3">
        {popularBrands.map((brand) => (
          <TouchableOpacity
            key={brand.id}
            className={`bg-white p-4 rounded-lg shadow-sm border-2 ${
              selectedBrands.includes(brand.id) 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200'
            }`}
            onPress={() => handleBrandSelection(brand.id)}
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className={`font-semibold ${
                  selectedBrands.includes(brand.id) ? 'text-blue-800' : 'text-gray-800'
                }`}>
                  {brand.name}
                </Text>
                <Text className="text-sm text-gray-600 mt-1">
                  Líneas: {brand.lines.join(', ')}
                </Text>
              </View>
              {selectedBrands.includes(brand.id) && (
                <CheckCircle size={24} color="#3B82F6" />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderAnalysisStep = () => (
    <View className="flex-1 justify-center items-center p-4">
      <View className="bg-orange-500 w-24 h-24 rounded-full items-center justify-center mb-6">
        <Beaker size={40} color="white" />
      </View>
      
      <Text className="text-2xl font-bold text-gray-800 mb-2">Análisis de Viabilidad</Text>
      <Text className="text-gray-600 text-center mb-4">
        Analizando factores de riesgo y generando fórmula experta
      </Text>
      
      <View className="bg-white rounded-lg p-4 w-full max-w-sm">
        <Text className="font-semibold text-gray-800 mb-2">Analizando:</Text>
        <Text className="text-sm text-gray-600">• Compatibilidad de productos</Text>
        <Text className="text-sm text-gray-600">• Análisis de riesgo capilar</Text>
        <Text className="text-sm text-gray-600">• Cálculo de sesiones necesarias</Text>
        <Text className="text-sm text-gray-600">• Generación de fórmula IA</Text>
        <Text className="text-sm text-gray-600">• Instrucciones paso a paso</Text>
      </View>
    </View>
  );

  const renderFormulationStep = () => {
    if (!viabilityAnalysis || !aiFormula || !finalFormula) return null;

    return (
      <View className="flex-1">
        {/* Análisis de viabilidad */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <View className="flex-row items-center mb-3">
            <Target size={20} color="#10B981" />
            <Text className="text-lg font-bold text-gray-800 ml-2">Análisis de Viabilidad</Text>
            <View className={`px-2 py-1 rounded-full ml-auto ${
              viabilityAnalysis.riskLevel === 'low' ? 'bg-green-100' :
              viabilityAnalysis.riskLevel === 'medium' ? 'bg-yellow-100' : 'bg-red-100'
            }`}>
              <Text className={`text-xs font-medium ${
                viabilityAnalysis.riskLevel === 'low' ? 'text-green-800' :
                viabilityAnalysis.riskLevel === 'medium' ? 'text-yellow-800' : 'text-red-800'
              }`}>
                Riesgo {viabilityAnalysis.riskLevel}
              </Text>
            </View>
          </View>

          <View className="grid grid-cols-2 gap-3 mb-3">
            <View className="bg-blue-50 p-3 rounded-lg">
              <Text className="font-medium text-blue-800">Sesiones</Text>
              <Text className="text-sm text-blue-600">{viabilityAnalysis.sessionsRequired}</Text>
            </View>
            <View className="bg-blue-50 p-3 rounded-lg">
              <Text className="font-medium text-blue-800">Cambio</Text>
              <Text className="text-sm text-blue-600">
                {viabilityAnalysis.lightening > 0 ? `+${viabilityAnalysis.lightening}` : `-${viabilityAnalysis.darkening}`} niveles
              </Text>
            </View>
          </View>

          {viabilityAnalysis.warnings.length > 0 && (
            <View className="bg-red-50 p-3 rounded-lg border border-red-200 mb-3">
              <View className="flex-row items-center mb-2">
                <AlertTriangle size={16} color="#EF4444" />
                <Text className="text-red-800 font-medium ml-2">Advertencias</Text>
              </View>
              {viabilityAnalysis.warnings.map((warning, index) => (
                <Text key={index} className="text-red-700 text-sm">• {warning}</Text>
              ))}
            </View>
          )}

          {viabilityAnalysis.recommendations.length > 0 && (
            <View className="bg-green-50 p-3 rounded-lg border border-green-200">
              <View className="flex-row items-center mb-2">
                <CheckCircle size={16} color="#10B981" />
                <Text className="text-green-800 font-medium ml-2">Recomendaciones</Text>
              </View>
              {viabilityAnalysis.recommendations.map((rec, index) => (
                <Text key={index} className="text-green-700 text-sm">• {rec}</Text>
              ))}
            </View>
          )}
        </View>

        {/* Fórmula IA */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <Zap size={20} color="#8B5CF6" />
              <Text className="text-lg font-bold text-gray-800 ml-2">Fórmula IA</Text>
            </View>
            <View className="bg-purple-100 px-2 py-1 rounded-full">
              <Text className="text-purple-800 text-xs font-medium">
                {Math.round(aiFormula.confidence)}% confianza
              </Text>
            </View>
          </View>

          <View className="space-y-3">
            {finalFormula.products.map((product, index) => (
              <View key={index} className="bg-gray-50 p-3 rounded-lg">
                <View className="flex-row items-center justify-between">
                  <View className="flex-1">
                    <Text className="font-medium text-gray-800">{product.name}</Text>
                    <Text className="text-sm text-gray-600">
                      {product.brand} - {product.line}
                    </Text>
                    <Text className="text-sm text-gray-600">
                      Código: {product.code} | Cantidad: {product.amount}
                    </Text>
                  </View>
                  <TouchableOpacity
                    className="bg-blue-100 p-2 rounded-lg"
                    onPress={() => handleProductEdit(index)}
                  >
                    <Edit3 size={16} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>

          <View className="bg-gray-50 p-3 rounded-lg mt-3">
            <Text className="font-medium text-gray-800 mb-2">Desarrollador</Text>
            <Text className="text-sm text-gray-600">
              Volumen: {finalFormula.developer.volume}vol | Proporción: {finalFormula.developer.ratio}
            </Text>
          </View>

          <View className="flex-row space-x-3 mt-3">
            <View className="flex-1 bg-orange-50 p-3 rounded-lg">
              <View className="flex-row items-center">
                <Clock size={16} color="#F97316" />
                <Text className="font-medium text-orange-800 ml-2">Tiempo</Text>
              </View>
              <Text className="text-sm text-orange-600">{finalFormula.processingTime} min</Text>
            </View>
            <View className="flex-1 bg-green-50 p-3 rounded-lg">
              <View className="flex-row items-center">
                <DollarSign size={16} color="#10B981" />
                <Text className="font-medium text-green-800 ml-2">Coste</Text>
              </View>
              <Text className="text-sm text-green-600">€{finalFormula.estimatedCost}</Text>
            </View>
          </View>
        </View>

        {/* Instrucciones */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Instrucciones Paso a Paso</Text>
          <View className="space-y-2">
            {finalFormula.instructions.map((instruction, index) => (
              <View key={index} className="flex-row items-start">
                <View className="bg-blue-500 w-6 h-6 rounded-full items-center justify-center mr-3 mt-0.5">
                  <Text className="text-white text-xs font-bold">{index + 1}</Text>
                </View>
                <Text className="flex-1 text-sm text-gray-700">{instruction}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Modificaciones del estilista */}
        {stylistOverrides.length > 0 && (
          <View className="bg-yellow-50 rounded-lg p-4 mb-4 border border-yellow-200">
            <View className="flex-row items-center mb-2">
              <Star size={16} color="#F59E0B" />
              <Text className="text-yellow-800 font-medium ml-2">Modificaciones del Estilista</Text>
            </View>
            {stylistOverrides.map((override, index) => (
              <Text key={index} className="text-yellow-700 text-sm">• {override}</Text>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderValidationStep = () => (
    <View className="flex-1 justify-center items-center p-4">
      <View className="bg-green-500 w-24 h-24 rounded-full items-center justify-center mb-6">
        <CheckCircle size={40} color="white" />
      </View>

      <Text className="text-2xl font-bold text-gray-800 mb-2">Formulación Completada</Text>
      <Text className="text-gray-600 text-center mb-4">
        Fórmula validada y lista para aplicación
      </Text>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Formulación Experta con IA
          </Text>
          <Text className="text-gray-600">
            Generación inteligente de fórmula con control total del estilista
          </Text>
        </View>

        {currentStep === 'brands' && renderBrandsStep()}
        {currentStep === 'analysis' && renderAnalysisStep()}
        {currentStep === 'formulation' && renderFormulationStep()}
        {currentStep === 'validation' && renderValidationStep()}
      </ScrollView>

      {/* Botones de acción */}
      {currentStep === 'brands' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={onBack}
            >
              <Text className="text-gray-800 font-semibold text-center">Volver</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-lg ${
                selectedBrands.length > 0 ? 'bg-orange-500' : 'bg-gray-300'
              }`}
              onPress={() => selectedBrands.length > 0 && performViabilityAnalysis()}
              disabled={selectedBrands.length === 0}
            >
              <Text className="text-white font-semibold text-center">
                {selectedBrands.length > 0
                  ? 'Generar Fórmula IA'
                  : 'Selecciona Marcas'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {currentStep === 'formulation' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={() => setCurrentStep('brands')}
            >
              <Text className="text-gray-800 font-semibold text-center">Cambiar Marcas</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="flex-1 bg-orange-500 py-3 rounded-lg"
              onPress={handleFormulationComplete}
            >
              <Text className="text-white font-semibold text-center">Confirmar Fórmula</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Modal de edición de producto */}
      <Modal visible={showEditModal} animationType="slide" transparent>
        <View className="flex-1 bg-black/50 justify-center px-4">
          <View className="bg-white rounded-lg p-6">
            <Text className="text-xl font-bold text-gray-800 mb-4">Editar Producto</Text>

            {editingProduct && (
              <View className="space-y-3">
                <View>
                  <Text className="text-sm font-medium text-gray-700 mb-1">Nombre</Text>
                  <TextInput
                    className="bg-gray-100 p-3 rounded-lg"
                    value={editingProduct.name}
                    onChangeText={(text) => setEditingProduct({...editingProduct, name: text})}
                  />
                </View>

                <View>
                  <Text className="text-sm font-medium text-gray-700 mb-1">Código</Text>
                  <TextInput
                    className="bg-gray-100 p-3 rounded-lg"
                    value={editingProduct.code}
                    onChangeText={(text) => setEditingProduct({...editingProduct, code: text})}
                  />
                </View>

                <View>
                  <Text className="text-sm font-medium text-gray-700 mb-1">Cantidad</Text>
                  <TextInput
                    className="bg-gray-100 p-3 rounded-lg"
                    value={editingProduct.amount}
                    onChangeText={(text) => setEditingProduct({...editingProduct, amount: text})}
                  />
                </View>
              </View>
            )}

            <View className="flex-row space-x-3 mt-6">
              <TouchableOpacity
                className="flex-1 bg-gray-200 py-3 rounded-lg"
                onPress={() => setShowEditModal(false)}
              >
                <Text className="text-gray-800 font-semibold text-center">Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="flex-1 bg-blue-500 py-3 rounded-lg"
                onPress={saveProductEdit}
              >
                <Text className="text-white font-semibold text-center">Guardar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ExpertFormulation;

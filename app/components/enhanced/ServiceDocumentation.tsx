import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Modal,
} from "react-native";
import {
  Camera,
  FileText,
  Star,
  Clock,
  DollarSign,
  CheckCircle,
  Heart,
  MessageCircle,
  Calendar,
  Shield,
  Save,
  Share2,
} from "lucide-react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { ServiceDocumentation, FormulationData, Client } from "../../types/ColorConsultationTypes";

interface ServiceDocumentationProps {
  client: Client;
  formulation: FormulationData;
  onDocumentationComplete: (documentation: ServiceDocumentation) => void;
  onBack: () => void;
}

const ServiceDocumentationComponent: React.FC<ServiceDocumentationProps> = ({
  client,
  formulation,
  onDocumentationComplete,
  onBack,
}) => {
  const [currentStep, setCurrentStep] = useState<'photos' | 'formula' | 'notes' | 'summary'>('photos');
  const [resultImages, setResultImages] = useState<ServiceDocumentation['resultImages']>([]);
  const [appliedFormula, setAppliedFormula] = useState<ServiceDocumentation['appliedFormula']>({
    products: [...formulation.finalFormula.products],
    developer: { ...formulation.finalFormula.developer },
    actualProcessingTime: formulation.finalFormula.processingTime,
    modifications: [],
  });
  const [stylistNotes, setStylistNotes] = useState("");
  const [clientFeedback, setClientFeedback] = useState("");
  const [clientSatisfaction, setClientSatisfaction] = useState<1 | 2 | 3 | 4 | 5>(5);
  const [maintenanceInstructions, setMaintenanceInstructions] = useState("");
  const [nextAppointmentRecommendation, setNextAppointmentRecommendation] = useState("");
  const [productRecommendations, setProductRecommendations] = useState<string[]>([]);
  const [serviceDuration, setServiceDuration] = useState(120);
  const [totalCost, setTotalCost] = useState(formulation.finalFormula.estimatedCost);
  const [showSummaryModal, setShowSummaryModal] = useState(false);

  const requiredPhotoZones: ServiceDocumentation['resultImages'][0]['zone'][] = ['frontal', 'lateral', 'posterior'];
  const optionalPhotoZones: ServiceDocumentation['resultImages'][0]['zone'][] = ['detalle'];

  useEffect(() => {
    // Generar recomendaciones automáticas
    generateAutoRecommendations();
  }, []);

  const generateAutoRecommendations = () => {
    const recommendations = [
      "Champú sin sulfatos para cabello teñido",
      "Mascarilla hidratante semanal",
      "Protector térmico antes del peinado",
    ];

    if (formulation.viabilityAnalysis.lightening > 2) {
      recommendations.push("Tratamiento reconstructor quincenal");
    }

    setProductRecommendations(recommendations);
    setMaintenanceInstructions(
      "Lavar con agua tibia, aplicar mascarilla 1-2 veces por semana, evitar exposición solar directa las primeras 48 horas."
    );
    setNextAppointmentRecommendation(
      formulation.viabilityAnalysis.sessionsRequired > 1 
        ? "Próxima sesión en 4-6 semanas para completar el proceso"
        : "Retoque de raíces en 6-8 semanas"
    );
  };

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permisos Requeridos',
        'Necesitamos acceso a la cámara para documentar el resultado.'
      );
      return false;
    }
    return true;
  };

  const simulateFaceBlurring = async (imageUri: string): Promise<string> => {
    console.log('Aplicando difuminado de rostro a resultado final:', imageUri);
    await new Promise(resolve => setTimeout(resolve, 500));
    return imageUri;
  };

  const captureResultPhoto = async (zone: ServiceDocumentation['resultImages'][0]['zone']) => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const originalUri = result.assets[0].uri;
        
        Alert.alert('Procesando...', 'Aplicando difuminado de rostro');
        
        const blurredUri = await simulateFaceBlurring(originalUri);
        
        const newResultImage = {
          id: `result_${Date.now()}_${zone}`,
          uri: blurredUri,
          zone,
          timestamp: new Date().toISOString(),
          faceBlurred: true,
        };

        // Reemplazar imagen existente de la misma zona o agregar nueva
        const updatedImages = resultImages.filter(img => img.zone !== zone);
        setResultImages([...updatedImages, newResultImage]);

        Alert.alert(
          'Foto Capturada',
          `Foto del resultado ${zone} capturada exitosamente con rostro difuminado.`
        );
      }
    } catch (error) {
      console.error('Error capturando foto del resultado:', error);
      Alert.alert('Error', 'No se pudo capturar la foto del resultado.');
    }
  };

  const hasZonePhoto = (zone: ServiceDocumentation['resultImages'][0]['zone']) => {
    return resultImages.some(img => img.zone === zone);
  };

  const canProceedToFormula = () => {
    return requiredPhotoZones.every(zone => hasZonePhoto(zone));
  };

  const getZoneDisplayName = (zone: ServiceDocumentation['resultImages'][0]['zone']) => {
    const names = {
      frontal: 'Frontal',
      lateral: 'Lateral',
      posterior: 'Posterior',
      detalle: 'Detalle',
    };
    return names[zone];
  };

  const handleDocumentationComplete = () => {
    if (!stylistNotes.trim()) {
      Alert.alert('Error', 'Por favor, añade notas del servicio.');
      return;
    }

    const documentation: ServiceDocumentation = {
      resultImages,
      appliedFormula,
      stylistNotes,
      clientFeedback,
      clientSatisfaction,
      maintenanceInstructions,
      nextAppointmentRecommendation,
      productRecommendations,
      serviceDate: new Date().toISOString(),
      duration: serviceDuration,
      totalCost,
    };

    onDocumentationComplete(documentation);
  };

  const renderPhotosStep = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Fotos del Resultado Final</Text>
        <Text className="text-gray-600 mb-4">
          Documenta el resultado final del servicio para el historial del cliente
        </Text>
        
        <View className="bg-green-50 p-3 rounded-lg border border-green-200 mb-4">
          <View className="flex-row items-center mb-2">
            <Shield size={16} color="#10B981" />
            <Text className="text-green-800 font-medium ml-2">Privacidad Protegida</Text>
          </View>
          <Text className="text-green-700 text-sm">
            • Rostros automáticamente difuminados{'\n'}
            • Solo se documenta el resultado del cabello{'\n'}
            • Imágenes vinculadas al historial del cliente
          </Text>
        </View>
      </View>

      {/* Fotos requeridas */}
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Fotos Requeridas</Text>
        <View className="space-y-3">
          {requiredPhotoZones.map(zone => (
            <TouchableOpacity
              key={zone}
              className={`p-3 rounded-lg border-2 ${
                hasZonePhoto(zone) 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-red-300 bg-red-50'
              }`}
              onPress={() => captureResultPhoto(zone)}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  <View className="flex-row items-center">
                    {hasZonePhoto(zone) ? (
                      <CheckCircle size={20} color="#10B981" />
                    ) : (
                      <Camera size={20} color="#EF4444" />
                    )}
                    <Text className={`font-semibold ml-2 ${
                      hasZonePhoto(zone) ? 'text-green-800' : 'text-red-800'
                    }`}>
                      Vista {getZoneDisplayName(zone)}
                    </Text>
                  </View>
                  <Text className="text-sm text-gray-600 mt-1">
                    Captura el resultado final desde este ángulo
                  </Text>
                </View>
                {hasZonePhoto(zone) && (
                  <View className="w-12 h-12 rounded-lg overflow-hidden ml-3">
                    <Image
                      source={{ uri: resultImages.find(img => img.zone === zone)?.uri }}
                      className="w-full h-full"
                      contentFit="cover"
                    />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Fotos opcionales */}
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Fotos Opcionales</Text>
        <View className="space-y-3">
          {optionalPhotoZones.map(zone => (
            <TouchableOpacity
              key={zone}
              className={`p-3 rounded-lg border-2 ${
                hasZonePhoto(zone) 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-gray-300 bg-gray-50'
              }`}
              onPress={() => captureResultPhoto(zone)}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  <View className="flex-row items-center">
                    {hasZonePhoto(zone) ? (
                      <CheckCircle size={20} color="#10B981" />
                    ) : (
                      <Camera size={20} color="#6B7280" />
                    )}
                    <Text className={`font-semibold ml-2 ${
                      hasZonePhoto(zone) ? 'text-green-800' : 'text-gray-700'
                    }`}>
                      {getZoneDisplayName(zone)}
                    </Text>
                  </View>
                  <Text className="text-sm text-gray-600 mt-1">
                    Foto de detalle del resultado
                  </Text>
                </View>
                {hasZonePhoto(zone) && (
                  <View className="w-12 h-12 rounded-lg overflow-hidden ml-3">
                    <Image
                      source={{ uri: resultImages.find(img => img.zone === zone)?.uri }}
                      className="w-full h-full"
                      contentFit="cover"
                    />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderFormulaStep = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Fórmula Aplicada</Text>
        <Text className="text-gray-600 mb-4">
          Confirma la fórmula final aplicada y cualquier modificación realizada
        </Text>
      </View>

      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Productos Utilizados</Text>
        <View className="space-y-3">
          {appliedFormula.products.map((product, index) => (
            <View key={index} className="bg-gray-50 p-3 rounded-lg">
              <Text className="font-medium text-gray-800">{product.name}</Text>
              <Text className="text-sm text-gray-600">
                {product.brand} - Código: {product.code} | Cantidad: {product.amount}
              </Text>
            </View>
          ))}
        </View>
      </View>

      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Tiempo de Procesamiento</Text>
        <View className="flex-row items-center space-x-3">
          <Text className="text-gray-600">Tiempo real:</Text>
          <TextInput
            className="bg-gray-100 px-3 py-2 rounded-lg flex-1"
            value={appliedFormula.actualProcessingTime.toString()}
            onChangeText={(text) => setAppliedFormula({
              ...appliedFormula,
              actualProcessingTime: parseInt(text) || 0
            })}
            keyboardType="numeric"
            placeholder="Minutos"
          />
          <Text className="text-gray-600">min</Text>
        </View>
      </View>

      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Modificaciones Realizadas</Text>
        <TextInput
          className="border border-gray-300 rounded-lg p-3 min-h-[80px] bg-white text-gray-800"
          multiline
          placeholder="Describe cualquier modificación realizada durante la aplicación..."
          value={appliedFormula.modifications.join('\n')}
          onChangeText={(text) => setAppliedFormula({
            ...appliedFormula,
            modifications: text.split('\n').filter(line => line.trim())
          })}
          textAlignVertical="top"
        />
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Documentación del Servicio
          </Text>
          <Text className="text-gray-600">
            Documenta el resultado final para el historial del cliente
          </Text>
        </View>

        {currentStep === 'photos' && renderPhotosStep()}
        {currentStep === 'formula' && renderFormulaStep()}
        {currentStep === 'notes' && (
          <Text className="text-center text-gray-600">Notas y valoración (próximamente)</Text>
        )}
        {currentStep === 'summary' && (
          <Text className="text-center text-gray-600">Resumen final (próximamente)</Text>
        )}
      </ScrollView>

      {/* Botones de acción */}
      {currentStep === 'photos' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={onBack}
            >
              <Text className="text-gray-800 font-semibold text-center">Volver</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-lg ${
                canProceedToFormula() ? 'bg-green-500' : 'bg-gray-300'
              }`}
              onPress={() => canProceedToFormula() && setCurrentStep('formula')}
              disabled={!canProceedToFormula()}
            >
              <Text className="text-white font-semibold text-center">
                {canProceedToFormula() 
                  ? 'Continuar' 
                  : 'Captura Fotos Requeridas'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {currentStep === 'formula' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={() => setCurrentStep('photos')}
            >
              <Text className="text-gray-800 font-semibold text-center">Volver</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="flex-1 bg-green-500 py-3 rounded-lg"
              onPress={() => setCurrentStep('notes')}
            >
              <Text className="text-white font-semibold text-center">Continuar</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default ServiceDocumentationComponent;

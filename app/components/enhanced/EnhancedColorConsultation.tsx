import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
} from "react-native";
import {
  ArrowLeft,
  Users,
  Camera,
  Palette,
  FlaskConical,
  FileText,
  CheckCircle,
  Clock,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Importar componentes mejorados
import ClientSelectionWithConsent from "./ClientSelectionWithConsent";
import EnhancedHairDiagnosis from "./EnhancedHairDiagnosis";
import DesiredColorDefinition from "./DesiredColorDefinition";
import ExpertFormulation from "./ExpertFormulation";
import ServiceDocumentationComponent from "./ServiceDocumentation";

// Importar tipos
import {
  ColorConsultationData,
  Client,
  ConsentData,
  HairDiagnosisData,
  DesiredColorData,
  FormulationData,
  ServiceDocumentation,
} from "../../types/ColorConsultationTypes";

interface EnhancedColorConsultationProps {
  onComplete?: (consultation: ColorConsultationData) => void;
  consultationId?: string; // Para editar consultas existentes
}

const EnhancedColorConsultation: React.FC<EnhancedColorConsultationProps> = ({
  onComplete = () => {},
  consultationId,
}) => {
  const router = useRouter();
  const [currentPhase, setCurrentPhase] = useState<ColorConsultationData['currentPhase']>('client_selection');
  const [consultationData, setConsultationData] = useState<Partial<ColorConsultationData>>({
    id: consultationId || `consultation_${Date.now()}`,
    currentPhase: 'client_selection',
    status: 'draft',
    createdAt: new Date().toISOString(),
    stylistId: 'current_stylist', // En producción, obtener del usuario actual
  });

  useEffect(() => {
    if (consultationId) {
      loadExistingConsultation(consultationId);
    }
  }, [consultationId]);

  const loadExistingConsultation = async (id: string) => {
    try {
      const stored = await AsyncStorage.getItem(`enhanced_consultation_${id}`);
      if (stored) {
        const consultation: ColorConsultationData = JSON.parse(stored);
        setConsultationData(consultation);
        setCurrentPhase(consultation.currentPhase);
      }
    } catch (error) {
      console.error('Error cargando consulta:', error);
      Alert.alert('Error', 'No se pudo cargar la consulta existente.');
    }
  };

  const saveConsultationDraft = async (data: Partial<ColorConsultationData>) => {
    try {
      const updatedData = {
        ...data,
        updatedAt: new Date().toISOString(),
      };
      await AsyncStorage.setItem(
        `enhanced_consultation_${data.id}`,
        JSON.stringify(updatedData)
      );
    } catch (error) {
      console.error('Error guardando borrador:', error);
    }
  };

  const handleClientSelected = async (client: Client, consent: ConsentData) => {
    const updatedData = {
      ...consultationData,
      client,
      consent,
      currentPhase: 'diagnosis' as const,
    };
    
    setConsultationData(updatedData);
    setCurrentPhase('diagnosis');
    await saveConsultationDraft(updatedData);
  };

  const handleDiagnosisComplete = async (diagnosis: HairDiagnosisData) => {
    const updatedData = {
      ...consultationData,
      diagnosis,
      currentPhase: 'color_definition' as const,
    };
    
    setConsultationData(updatedData);
    setCurrentPhase('color_definition');
    await saveConsultationDraft(updatedData);
  };

  const handleColorDefinitionComplete = async (desiredColor: DesiredColorData) => {
    const updatedData = {
      ...consultationData,
      desiredColor,
      currentPhase: 'formulation' as const,
    };
    
    setConsultationData(updatedData);
    setCurrentPhase('formulation');
    await saveConsultationDraft(updatedData);
  };

  const handleFormulationComplete = async (formulation: FormulationData) => {
    const updatedData = {
      ...consultationData,
      formulation,
      currentPhase: 'documentation' as const,
    };
    
    setConsultationData(updatedData);
    setCurrentPhase('documentation');
    await saveConsultationDraft(updatedData);
  };

  const handleDocumentationComplete = async (documentation: ServiceDocumentation) => {
    const finalData: ColorConsultationData = {
      ...consultationData,
      documentation,
      currentPhase: 'completed',
      status: 'completed',
      completedAt: new Date().toISOString(),
    } as ColorConsultationData;
    
    setConsultationData(finalData);
    setCurrentPhase('completed');
    
    // Guardar consulta completada
    await saveConsultationDraft(finalData);
    
    // Actualizar historial del cliente
    await updateClientHistory(finalData);
    
    Alert.alert(
      'Consulta Completada',
      'La consulta de color ultra-inteligente ha sido completada exitosamente.',
      [
        {
          text: 'Ver Resumen',
          onPress: () => showConsultationSummary(finalData),
        },
        {
          text: 'Nueva Consulta',
          onPress: () => {
            onComplete(finalData);
            router.push('/');
          },
        },
      ]
    );
  };

  const updateClientHistory = async (consultation: ColorConsultationData) => {
    try {
      // En una implementación real, aquí se actualizaría la base de datos del cliente
      console.log('Actualizando historial del cliente:', consultation.client.id);
    } catch (error) {
      console.error('Error actualizando historial del cliente:', error);
    }
  };

  const showConsultationSummary = (consultation: ColorConsultationData) => {
    // Mostrar resumen detallado de la consulta
    Alert.alert(
      'Resumen de la Consulta',
      `Cliente: ${consultation.client.name}\n` +
      `Diagnóstico: Nivel ${consultation.diagnosis.roots.naturalLevel} (${consultation.diagnosis.roots.undertone})\n` +
      `Color deseado: Nivel ${consultation.desiredColor.targetLevel}\n` +
      `Técnica: ${consultation.desiredColor.technique}\n` +
      `Sesiones requeridas: ${consultation.formulation.viabilityAnalysis.sessionsRequired}\n` +
      `Coste total: €${consultation.documentation?.totalCost}\n` +
      `Satisfacción: ${consultation.documentation?.clientSatisfaction}/5 ⭐`
    );
  };

  const handleBack = () => {
    switch (currentPhase) {
      case 'diagnosis':
        setCurrentPhase('client_selection');
        break;
      case 'color_definition':
        setCurrentPhase('diagnosis');
        break;
      case 'formulation':
        setCurrentPhase('color_definition');
        break;
      case 'documentation':
        setCurrentPhase('formulation');
        break;
      default:
        router.back();
    }
  };

  const getPhaseIcon = (phase: ColorConsultationData['currentPhase']) => {
    const icons = {
      client_selection: Users,
      diagnosis: Camera,
      color_definition: Palette,
      formulation: FlaskConical,
      documentation: FileText,
      completed: CheckCircle,
    };
    return icons[phase];
  };

  const getPhaseTitle = (phase: ColorConsultationData['currentPhase']) => {
    const titles = {
      client_selection: 'Selección de Cliente',
      diagnosis: 'Diagnóstico Capilar',
      color_definition: 'Color Deseado',
      formulation: 'Formulación Experta',
      documentation: 'Documentación',
      completed: 'Completado',
    };
    return titles[phase];
  };

  const renderProgressIndicator = () => {
    const phases: ColorConsultationData['currentPhase'][] = [
      'client_selection',
      'diagnosis',
      'color_definition',
      'formulation',
      'documentation',
      'completed',
    ];

    const currentIndex = phases.indexOf(currentPhase);

    return (
      <View className="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity
            onPress={handleBack}
            className="flex-row items-center"
          >
            <ArrowLeft size={24} color="#374151" />
            <Text className="text-gray-800 font-semibold ml-2">
              {currentPhase === 'client_selection' ? 'Inicio' : 'Volver'}
            </Text>
          </TouchableOpacity>
          
          <View className="flex-row items-center">
            <Text className="text-gray-600 text-sm mr-2">
              {currentIndex + 1}/{phases.length}
            </Text>
            <View className="bg-blue-100 px-3 py-1 rounded-full">
              <Text className="text-blue-800 font-semibold text-sm">
                {getPhaseTitle(currentPhase)}
              </Text>
            </View>
          </View>
        </View>
        
        {/* Barra de progreso */}
        <View className="mt-3">
          <View className="bg-gray-200 h-2 rounded-full">
            <View 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentIndex + 1) / phases.length) * 100}%` }}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'client_selection':
        return (
          <ClientSelectionWithConsent
            onClientSelected={handleClientSelected}
            onCancel={() => router.back()}
          />
        );
      
      case 'diagnosis':
        return (
          <EnhancedHairDiagnosis
            onDiagnosisComplete={handleDiagnosisComplete}
            onBack={handleBack}
          />
        );
      
      case 'color_definition':
        return (
          <DesiredColorDefinition
            diagnosis={consultationData.diagnosis!}
            onColorDefinitionComplete={handleColorDefinitionComplete}
            onBack={handleBack}
          />
        );
      
      case 'formulation':
        return (
          <ExpertFormulation
            diagnosis={consultationData.diagnosis!}
            desiredColor={consultationData.desiredColor!}
            onFormulationComplete={handleFormulationComplete}
            onBack={handleBack}
          />
        );
      
      case 'documentation':
        return (
          <ServiceDocumentationComponent
            client={consultationData.client!}
            formulation={consultationData.formulation!}
            onDocumentationComplete={handleDocumentationComplete}
            onBack={handleBack}
          />
        );
      
      case 'completed':
        return (
          <View className="flex-1 justify-center items-center p-4">
            <View className="bg-green-500 w-24 h-24 rounded-full items-center justify-center mb-6">
              <CheckCircle size={40} color="white" />
            </View>
            
            <Text className="text-2xl font-bold text-gray-800 mb-2">
              ¡Consulta Completada!
            </Text>
            <Text className="text-gray-600 text-center mb-6">
              La consulta de color ultra-inteligente ha sido completada exitosamente
            </Text>
            
            <TouchableOpacity
              className="bg-blue-500 px-6 py-3 rounded-lg"
              onPress={() => {
                onComplete(consultationData as ColorConsultationData);
                router.push('/');
              }}
            >
              <Text className="text-white font-semibold">Finalizar</Text>
            </TouchableOpacity>
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
      
      {renderProgressIndicator()}
      
      <View className="flex-1">
        {renderCurrentPhase()}
      </View>
    </SafeAreaView>
  );
};

export default EnhancedColorConsultation;

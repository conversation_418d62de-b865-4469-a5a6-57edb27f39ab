import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Modal,
  SafeAreaView,
} from "react-native";
import {
  Search,
  User,
  UserPlus,
  Shield,
  Camera,
  FileText,
  CheckCircle,
  AlertTriangle,
  Clock,
  X,
} from "lucide-react-native";
import { Image } from "expo-image";
import { Client, ConsentData } from "../../types/ColorConsultationTypes";

interface ClientSelectionWithConsentProps {
  onClientSelected: (client: Client, consent: ConsentData) => void;
  onCancel: () => void;
}

const ClientSelectionWithConsent: React.FC<ClientSelectionWithConsentProps> = ({
  onClientSelected,
  onCancel,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [showGuestForm, setShowGuestForm] = useState(false);
  const [guestName, setGuestName] = useState("");
  const [guestPhone, setGuestPhone] = useState("");
  const [consentData, setConsentData] = useState<Partial<ConsentData>>({
    imageCapture: false,
    dataAnalysis: false,
    dataStorage: false,
    aiProcessing: false,
  });

  useEffect(() => {
    loadClients();
  }, []);

  useEffect(() => {
    filterClients();
  }, [searchQuery, clients]);

  const loadClients = async () => {
    // Simular carga de clientes desde AsyncStorage o API
    const mockClients: Client[] = [
      {
        id: "1",
        name: "María García",
        email: "<EMAIL>",
        phone: "+34 612 345 678",
        image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Maria",
        lastVisit: "hace 2 semanas",
        consentGiven: true,
      },
      {
        id: "2",
        name: "Ana Martínez",
        email: "<EMAIL>",
        phone: "+34 623 456 789",
        image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Ana",
        lastVisit: "hace 1 mes",
        consentGiven: false,
      },
      {
        id: "3",
        name: "Carmen López",
        email: "<EMAIL>",
        phone: "+34 634 567 890",
        image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Carmen",
        lastVisit: "hace 3 días",
        consentGiven: true,
      },
    ];
    setClients(mockClients);
  };

  const filterClients = () => {
    if (!searchQuery.trim()) {
      setFilteredClients(clients);
      return;
    }

    const filtered = clients.filter(
      (client) =>
        client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.phone?.includes(searchQuery) ||
        client.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredClients(filtered);
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    if (client.consentGiven) {
      // Si ya tiene consentimiento, proceder directamente
      const existingConsent: ConsentData = {
        imageCapture: true,
        dataAnalysis: true,
        dataStorage: true,
        aiProcessing: true,
        timestamp: client.consentTimestamp || new Date().toISOString(),
        stylistWitness: "Current Stylist", // En producción, obtener del usuario actual
      };
      onClientSelected(client, existingConsent);
    } else {
      // Mostrar modal de consentimiento
      setShowConsentModal(true);
    }
  };

  const handleGuestClient = () => {
    if (!guestName.trim()) {
      Alert.alert("Error", "Por favor, introduce el nombre del cliente");
      return;
    }

    const guestClient: Client = {
      id: `guest_${Date.now()}`,
      name: guestName,
      phone: guestPhone,
      isGuest: true,
      consentGiven: false,
    };

    setSelectedClient(guestClient);
    setShowGuestForm(false);
    setShowConsentModal(true);
  };

  const handleConsentSubmit = () => {
    const { imageCapture, dataAnalysis, dataStorage, aiProcessing } = consentData;

    if (!imageCapture || !dataAnalysis || !aiProcessing) {
      Alert.alert(
        "Consentimiento Requerido",
        "Para realizar la consulta de color con IA, es necesario el consentimiento para captura de imágenes, análisis de datos y procesamiento con IA."
      );
      return;
    }

    const finalConsent: ConsentData = {
      imageCapture: imageCapture!,
      dataAnalysis: dataAnalysis!,
      dataStorage: dataStorage!,
      aiProcessing: aiProcessing!,
      timestamp: new Date().toISOString(),
      stylistWitness: "Current Stylist", // En producción, obtener del usuario actual
    };

    // Actualizar cliente con consentimiento
    const updatedClient: Client = {
      ...selectedClient!,
      consentGiven: true,
      consentTimestamp: finalConsent.timestamp,
    };

    setShowConsentModal(false);
    onClientSelected(updatedClient, finalConsent);
  };

  const renderClientItem = (client: Client) => (
    <TouchableOpacity
      key={client.id}
      className="bg-white p-4 rounded-lg mb-3 shadow-sm border border-gray-100"
      onPress={() => handleClientSelect(client)}
    >
      <View className="flex-row items-center">
        <Image
          source={{ uri: client.image || "https://api.dicebear.com/7.x/avataaars/svg?seed=default" }}
          className="w-12 h-12 rounded-full bg-gray-200"
        />
        <View className="flex-1 ml-3">
          <Text className="font-semibold text-gray-800">{client.name}</Text>
          {client.lastVisit && (
            <Text className="text-gray-500 text-sm">Última visita: {client.lastVisit}</Text>
          )}
          {client.phone && (
            <Text className="text-gray-500 text-sm">{client.phone}</Text>
          )}
        </View>
        <View className="items-end">
          {client.consentGiven ? (
            <View className="flex-row items-center">
              <CheckCircle size={16} color="#10B981" />
              <Text className="text-green-600 text-xs ml-1">Consentimiento</Text>
            </View>
          ) : (
            <View className="flex-row items-center">
              <AlertTriangle size={16} color="#F59E0B" />
              <Text className="text-amber-600 text-xs ml-1">Sin consentimiento</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderConsentModal = () => (
    <Modal visible={showConsentModal} animationType="slide" transparent>
      <View className="flex-1 bg-black/50 justify-center px-4">
        <View className="bg-white rounded-lg p-6 max-h-[80%]">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-xl font-bold text-gray-800">Consentimiento Informado</Text>
            <TouchableOpacity onPress={() => setShowConsentModal(false)}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            <View className="bg-blue-50 p-4 rounded-lg mb-4">
              <View className="flex-row items-center mb-2">
                <Shield size={20} color="#3B82F6" />
                <Text className="text-blue-800 font-semibold ml-2">Protección de Datos</Text>
              </View>
              <Text className="text-blue-700 text-sm">
                Para realizar la consulta de color con IA, necesitamos su consentimiento explícito para:
              </Text>
            </View>

            <View className="space-y-4">
              <TouchableOpacity
                className={`p-4 rounded-lg border-2 ${
                  consentData.imageCapture ? "border-green-500 bg-green-50" : "border-gray-300"
                }`}
                onPress={() => setConsentData({ ...consentData, imageCapture: !consentData.imageCapture })}
              >
                <View className="flex-row items-center">
                  <Camera size={20} color={consentData.imageCapture ? "#10B981" : "#6B7280"} />
                  <Text className={`font-semibold ml-2 ${consentData.imageCapture ? "text-green-800" : "text-gray-700"}`}>
                    Captura de Imágenes
                  </Text>
                </View>
                <Text className="text-sm text-gray-600 mt-1">
                  Tomar fotografías del cabello para análisis. Los rostros serán automáticamente difuminados.
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`p-4 rounded-lg border-2 ${
                  consentData.dataAnalysis ? "border-green-500 bg-green-50" : "border-gray-300"
                }`}
                onPress={() => setConsentData({ ...consentData, dataAnalysis: !consentData.dataAnalysis })}
              >
                <View className="flex-row items-center">
                  <FileText size={20} color={consentData.dataAnalysis ? "#10B981" : "#6B7280"} />
                  <Text className={`font-semibold ml-2 ${consentData.dataAnalysis ? "text-green-800" : "text-gray-700"}`}>
                    Análisis de Datos
                  </Text>
                </View>
                <Text className="text-sm text-gray-600 mt-1">
                  Analizar las imágenes y datos capilares para generar diagnóstico y recomendaciones.
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`p-4 rounded-lg border-2 ${
                  consentData.aiProcessing ? "border-green-500 bg-green-50" : "border-gray-300"
                }`}
                onPress={() => setConsentData({ ...consentData, aiProcessing: !consentData.aiProcessing })}
              >
                <View className="flex-row items-center">
                  <User size={20} color={consentData.aiProcessing ? "#10B981" : "#6B7280"} />
                  <Text className={`font-semibold ml-2 ${consentData.aiProcessing ? "text-green-800" : "text-gray-700"}`}>
                    Procesamiento con IA
                  </Text>
                </View>
                <Text className="text-sm text-gray-600 mt-1">
                  Utilizar inteligencia artificial para análisis capilar y formulación de color.
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`p-4 rounded-lg border-2 ${
                  consentData.dataStorage ? "border-green-500 bg-green-50" : "border-gray-300"
                }`}
                onPress={() => setConsentData({ ...consentData, dataStorage: !consentData.dataStorage })}
              >
                <View className="flex-row items-center">
                  <Clock size={20} color={consentData.dataStorage ? "#10B981" : "#6B7280"} />
                  <Text className={`font-semibold ml-2 ${consentData.dataStorage ? "text-green-800" : "text-gray-700"}`}>
                    Almacenamiento de Datos
                  </Text>
                </View>
                <Text className="text-sm text-gray-600 mt-1">
                  Guardar los datos de la consulta en el historial del cliente para futuras referencias.
                </Text>
              </TouchableOpacity>
            </View>

            <View className="bg-gray-50 p-4 rounded-lg mt-4">
              <Text className="text-gray-700 text-sm">
                <Text className="font-semibold">Nota:</Text> Puede retirar su consentimiento en cualquier momento. 
                Los datos se manejan según nuestra política de privacidad y normativas de protección de datos.
              </Text>
            </View>
          </ScrollView>

          <View className="flex-row space-x-3 mt-6">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={() => setShowConsentModal(false)}
            >
              <Text className="text-gray-800 font-semibold text-center">Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-lg ${
                consentData.imageCapture && consentData.dataAnalysis && consentData.aiProcessing
                  ? "bg-blue-500"
                  : "bg-gray-300"
              }`}
              onPress={handleConsentSubmit}
              disabled={!consentData.imageCapture || !consentData.dataAnalysis || !consentData.aiProcessing}
            >
              <Text className="text-white font-semibold text-center">Confirmar Consentimiento</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-1 p-4">
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">Selección de Cliente</Text>
          <Text className="text-gray-600">
            Busca un cliente existente o crea uno nuevo para la consulta de color
          </Text>
        </View>

        {/* Barra de búsqueda */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <View className="flex-row items-center bg-gray-100 rounded-lg px-3 py-2">
            <Search size={20} color="#6B7280" />
            <TextInput
              className="flex-1 ml-2 text-gray-800"
              placeholder="Buscar por nombre, teléfono o email..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>

        {/* Opciones rápidas */}
        <View className="flex-row space-x-3 mb-4">
          <TouchableOpacity
            className="flex-1 bg-blue-500 p-3 rounded-lg flex-row items-center justify-center"
            onPress={() => setShowGuestForm(true)}
          >
            <UserPlus size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Cliente Invitado</Text>
          </TouchableOpacity>
        </View>

        {/* Lista de clientes */}
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          {filteredClients.length > 0 ? (
            filteredClients.map(renderClientItem)
          ) : (
            <View className="bg-white p-8 rounded-lg items-center">
              <User size={48} color="#9CA3AF" />
              <Text className="text-gray-500 text-center mt-4">
                {searchQuery ? "No se encontraron clientes" : "No hay clientes registrados"}
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Modal para cliente invitado */}
        <Modal visible={showGuestForm} animationType="slide" transparent>
          <View className="flex-1 bg-black/50 justify-center px-4">
            <View className="bg-white rounded-lg p-6">
              <Text className="text-xl font-bold text-gray-800 mb-4">Cliente Invitado</Text>
              
              <TextInput
                className="bg-gray-100 p-3 rounded-lg mb-3"
                placeholder="Nombre completo *"
                value={guestName}
                onChangeText={setGuestName}
              />
              
              <TextInput
                className="bg-gray-100 p-3 rounded-lg mb-4"
                placeholder="Teléfono (opcional)"
                value={guestPhone}
                onChangeText={setGuestPhone}
                keyboardType="phone-pad"
              />
              
              <View className="flex-row space-x-3">
                <TouchableOpacity
                  className="flex-1 bg-gray-200 py-3 rounded-lg"
                  onPress={() => setShowGuestForm(false)}
                >
                  <Text className="text-gray-800 font-semibold text-center">Cancelar</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  className="flex-1 bg-blue-500 py-3 rounded-lg"
                  onPress={handleGuestClient}
                >
                  <Text className="text-white font-semibold text-center">Continuar</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {renderConsentModal()}
      </View>
    </SafeAreaView>
  );
};

export default ClientSelectionWithConsent;

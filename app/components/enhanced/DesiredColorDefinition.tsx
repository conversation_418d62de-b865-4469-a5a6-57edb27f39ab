import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Modal,
  Dimensions,
} from "react-native";
import {
  Camera,
  Palette,
  Target,
  CheckCircle,
  AlertTriangle,
  Eye,
  Sparkles,
  Image as ImageIcon,
  Shield,
  RefreshCw,
  Slide<PERSON>,
} from "lucide-react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { DesiredColorData, HairDiagnosisData } from "../../types/ColorConsultationTypes";

const { width } = Dimensions.get("window");

interface DesiredColorDefinitionProps {
  diagnosis: HairDiagnosisData;
  onColorDefinitionComplete: (colorData: DesiredColorData) => void;
  onBack: () => void;
}

const DesiredColorDefinition: React.FC<DesiredColorDefinitionProps> = ({
  diagnosis,
  onColorDefinitionComplete,
  onBack,
}) => {
  const [currentStep, setCurrentStep] = useState<'reference' | 'analysis' | 'definition'>('reference');
  const [referenceImages, setReferenceImages] = useState<DesiredColorData['referenceImages']>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [colorAnalysis, setColorAnalysis] = useState<any>(null);
  const [selectedTechnique, setSelectedTechnique] = useState<DesiredColorData['technique']>('global');
  const [selectedIntensity, setSelectedIntensity] = useState<DesiredColorData['intensity']>('medium');
  const [stylistDescription, setStylistDescription] = useState("");
  const [clientPreferences, setClientPreferences] = useState("");
  const [showColorPalette, setShowColorPalette] = useState(false);

  const techniques = [
    { id: 'global', name: 'Color Global', description: 'Color uniforme en todo el cabello' },
    { id: 'highlights', name: 'Mechas', description: 'Mechas tradicionales con gorro o papel' },
    { id: 'lowlights', name: 'Lowlights', description: 'Mechas más oscuras que el color base' },
    { id: 'balayage', name: 'Balayage', description: 'Técnica a mano alzada natural' },
    { id: 'ombre', name: 'Ombré', description: 'Degradado de oscuro a claro' },
    { id: 'color_melting', name: 'Color Melting', description: 'Fusión suave de colores' },
    { id: 'fantasy', name: 'Fantasía', description: 'Colores vibrantes o no naturales' },
  ];

  const intensities = [
    { id: 'subtle', name: 'Sutil', description: '1-2 niveles de cambio' },
    { id: 'medium', name: 'Medio', description: '3-4 niveles de cambio' },
    { id: 'dramatic', name: 'Dramático', description: '5+ niveles de cambio' },
  ];

  const colorPalette = [
    { level: 1, name: 'Negro', hex: '#1a1a1a' },
    { level: 2, name: 'Castaño Muy Oscuro', hex: '#2d1b14' },
    { level: 3, name: 'Castaño Oscuro', hex: '#3d2817' },
    { level: 4, name: 'Castaño Medio', hex: '#4a3728' },
    { level: 5, name: 'Castaño Claro', hex: '#6b4423' },
    { level: 6, name: 'Rubio Oscuro', hex: '#8b5a2b' },
    { level: 7, name: 'Rubio Medio', hex: '#a67c52' },
    { level: 8, name: 'Rubio Claro', hex: '#c19a6b' },
    { level: 9, name: 'Rubio Muy Claro', hex: '#d4b896' },
    { level: 10, name: 'Rubio Platino', hex: '#e8d5c4' },
  ];

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permisos Requeridos',
        'Necesitamos acceso a la cámara para capturar imágenes de referencia.'
      );
      return false;
    }
    return true;
  };

  const simulateFaceBlurring = async (imageUri: string): Promise<string> => {
    console.log('Aplicando difuminado de rostro a imagen de referencia:', imageUri);
    await new Promise(resolve => setTimeout(resolve, 500));
    return imageUri;
  };

  const captureReferenceImage = async () => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    Alert.alert(
      "Imagen de Referencia",
      "¿Cómo quieres añadir la imagen de referencia?",
      [
        {
          text: "Tomar Foto",
          onPress: () => captureFromCamera(),
        },
        {
          text: "Galería",
          onPress: () => selectFromGallery(),
        },
        {
          text: "Cancelar",
          style: "cancel",
        },
      ],
    );
  };

  const captureFromCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        await processReferenceImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error capturando imagen:', error);
      Alert.alert('Error', 'No se pudo capturar la imagen.');
    }
  };

  const selectFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        await processReferenceImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error seleccionando imagen:', error);
      Alert.alert('Error', 'No se pudo seleccionar la imagen.');
    }
  };

  const processReferenceImage = async (imageUri: string) => {
    try {
      Alert.alert('Procesando...', 'Aplicando difuminado de rostro y analizando color');
      
      // Aplicar difuminado de rostro
      const blurredUri = await simulateFaceBlurring(imageUri);
      
      // Simular análisis IA del color
      const analysis = {
        level: 7 + Math.random() * 2, // Nivel 7-9
        undertone: ['warm', 'cool', 'neutral'][Math.floor(Math.random() * 3)] as 'warm' | 'cool' | 'neutral',
        reflections: ['dorado', 'ceniza', 'cobrizo'][Math.floor(Math.random() * 3)],
        technique: 'balayage',
        luminosity: 70 + Math.random() * 20,
        saturation: 60 + Math.random() * 30,
      };

      const newReferenceImage = {
        id: `ref_${Date.now()}`,
        uri: blurredUri,
        analysis,
        faceBlurred: true,
      };

      setReferenceImages([...referenceImages, newReferenceImage]);

      Alert.alert(
        'Imagen Procesada',
        `Imagen de referencia añadida exitosamente.\n\nAnálisis detectado:\nNivel: ${Math.round(analysis.level)}\nSubtono: ${analysis.undertone}\nReflejo: ${analysis.reflections}`
      );
    } catch (error) {
      console.error('Error procesando imagen:', error);
      Alert.alert('Error', 'Error procesando la imagen de referencia.');
    }
  };

  const analyzeReferences = async () => {
    if (referenceImages.length === 0) {
      Alert.alert('Error', 'Añade al menos una imagen de referencia para continuar.');
      return;
    }

    setIsAnalyzing(true);
    setCurrentStep('analysis');

    try {
      // Simular análisis IA de las referencias
      await new Promise(resolve => setTimeout(resolve, 2000));

      const avgLevel = referenceImages.reduce((sum, img) => sum + img.analysis.level, 0) / referenceImages.length;
      const mostCommonUndertone = referenceImages[0].analysis.undertone; // Simplificado
      const allReflections = referenceImages.map(img => img.analysis.reflections);

      const analysisResult = {
        targetLevel: Math.round(avgLevel),
        targetUndertone: mostCommonUndertone,
        targetReflections: [...new Set(allReflections)],
        confidence: 85 + Math.random() * 10,
        recommendations: [
          'Color alcanzable en 1-2 sesiones',
          'Recomendado pre-tratamiento para mejor resultado',
          'Mantenimiento cada 6-8 semanas',
        ],
      };

      setColorAnalysis(analysisResult);
      setCurrentStep('definition');

      Alert.alert(
        'Análisis Completado',
        `Análisis de ${referenceImages.length} imagen(es) completado.\n\nColor objetivo detectado:\nNivel: ${analysisResult.targetLevel}\nSubtono: ${analysisResult.targetUndertone}`
      );
    } catch (error) {
      console.error('Error en análisis:', error);
      Alert.alert('Error', 'Error durante el análisis de referencias.');
      setCurrentStep('reference');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleDefinitionComplete = () => {
    if (!colorAnalysis) return;

    if (!stylistDescription.trim()) {
      Alert.alert('Error', 'Por favor, añade una descripción del color deseado.');
      return;
    }

    const finalColorData: DesiredColorData = {
      referenceImages,
      targetLevel: colorAnalysis.targetLevel,
      targetUndertone: colorAnalysis.targetUndertone,
      targetReflections: colorAnalysis.targetReflections,
      technique: selectedTechnique,
      intensity: selectedIntensity,
      stylistDescription,
      clientPreferences,
      stylistApproval: true,
      clientApproval: false, // Se confirmará después
    };

    onColorDefinitionComplete(finalColorData);
  };

  const renderReferenceStep = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Imágenes de Referencia</Text>
        <Text className="text-gray-600 mb-4">
          Añade 1-3 imágenes del color deseado para análisis IA preciso
        </Text>
        
        <View className="bg-blue-50 p-3 rounded-lg border border-blue-200 mb-4">
          <View className="flex-row items-center mb-2">
            <Shield size={16} color="#3B82F6" />
            <Text className="text-blue-800 font-medium ml-2">Privacidad Garantizada</Text>
          </View>
          <Text className="text-blue-700 text-sm">
            • Rostros automáticamente difuminados{'\n'}
            • Análisis solo del color del cabello{'\n'}
            • Imágenes procesadas localmente
          </Text>
        </View>

        <TouchableOpacity
          className="bg-blue-500 p-4 rounded-lg flex-row items-center justify-center mb-4"
          onPress={captureReferenceImage}
        >
          <Camera size={20} color="white" />
          <Text className="text-white font-semibold ml-2">Añadir Imagen de Referencia</Text>
        </TouchableOpacity>

        {referenceImages.length > 0 && (
          <View>
            <Text className="font-semibold text-gray-800 mb-3">
              Imágenes Capturadas ({referenceImages.length}/3)
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {referenceImages.map((img, index) => (
                <View key={img.id} className="mr-3">
                  <View className="w-24 h-24 rounded-lg overflow-hidden bg-gray-200">
                    <Image
                      source={{ uri: img.uri }}
                      className="w-full h-full"
                      contentFit="cover"
                    />
                  </View>
                  <View className="bg-white p-2 rounded-lg mt-2 shadow-sm">
                    <Text className="text-xs text-gray-600">Nivel: {Math.round(img.analysis.level)}</Text>
                    <Text className="text-xs text-gray-600">{img.analysis.undertone}</Text>
                  </View>
                </View>
              ))}
            </ScrollView>
          </View>
        )}
      </View>

      {/* Paleta de colores digital */}
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <View className="flex-row items-center justify-between mb-3">
          <Text className="font-semibold text-gray-800">Paleta Digital</Text>
          <TouchableOpacity
            className="bg-purple-100 px-3 py-1 rounded-full"
            onPress={() => setShowColorPalette(!showColorPalette)}
          >
            <Text className="text-purple-800 text-sm">
              {showColorPalette ? 'Ocultar' : 'Mostrar'}
            </Text>
          </TouchableOpacity>
        </View>
        
        {showColorPalette && (
          <View className="flex-row flex-wrap">
            {colorPalette.map((color) => (
              <TouchableOpacity
                key={color.level}
                className="w-12 h-12 rounded-lg mr-2 mb-2 border border-gray-300"
                style={{ backgroundColor: color.hex }}
                onPress={() => {
                  Alert.alert('Color Seleccionado', `${color.name} (Nivel ${color.level})`);
                }}
              />
            ))}
          </View>
        )}
      </View>
    </View>
  );

  const renderAnalysisStep = () => (
    <View className="flex-1 justify-center items-center p-4">
      <View className="bg-purple-500 w-24 h-24 rounded-full items-center justify-center mb-6">
        <Sparkles size={40} color="white" />
      </View>
      
      <Text className="text-2xl font-bold text-gray-800 mb-2">Analizando Referencias</Text>
      <Text className="text-gray-600 text-center mb-4">
        Analizando {referenceImages.length} imagen(es) de referencia con IA
      </Text>
      
      <View className="bg-white rounded-lg p-4 w-full max-w-sm">
        <Text className="font-semibold text-gray-800 mb-2">Procesando:</Text>
        <Text className="text-sm text-gray-600">• Detección de niveles de color</Text>
        <Text className="text-sm text-gray-600">• Análisis de subtonos</Text>
        <Text className="text-sm text-gray-600">• Identificación de reflejos</Text>
        <Text className="text-sm text-gray-600">• Evaluación de técnica</Text>
        <Text className="text-sm text-gray-600">• Cálculo de viabilidad</Text>
      </View>
    </View>
  );

  const renderDefinitionStep = () => {
    if (!colorAnalysis) return null;

    return (
      <View className="flex-1">
        {/* Análisis de referencias */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <View className="flex-row items-center mb-3">
            <CheckCircle size={20} color="#10B981" />
            <Text className="text-lg font-bold text-green-800 ml-2">Análisis Completado</Text>
            <View className="bg-green-100 px-2 py-1 rounded-full ml-auto">
              <Text className="text-green-800 text-xs font-medium">
                {Math.round(colorAnalysis.confidence)}% confianza
              </Text>
            </View>
          </View>

          <View className="bg-gray-50 p-3 rounded-lg mb-3">
            <Text className="font-medium text-gray-800 mb-2">Color Objetivo Detectado:</Text>
            <Text className="text-sm text-gray-600">
              Nivel: {colorAnalysis.targetLevel} | Subtono: {colorAnalysis.targetUndertone}
            </Text>
            <Text className="text-sm text-gray-600">
              Reflejos: {colorAnalysis.targetReflections.join(', ')}
            </Text>
          </View>

          <View className="space-y-2">
            {colorAnalysis.recommendations.map((rec: string, index: number) => (
              <Text key={index} className="text-sm text-gray-600">• {rec}</Text>
            ))}
          </View>
        </View>

        {/* Selección de técnica */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Técnica de Aplicación</Text>
          <View className="space-y-2">
            {techniques.map((technique) => (
              <TouchableOpacity
                key={technique.id}
                className={`p-3 rounded-lg border-2 ${
                  selectedTechnique === technique.id
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-300'
                }`}
                onPress={() => setSelectedTechnique(technique.id as DesiredColorData['technique'])}
              >
                <Text className={`font-medium ${
                  selectedTechnique === technique.id ? 'text-purple-800' : 'text-gray-800'
                }`}>
                  {technique.name}
                </Text>
                <Text className="text-sm text-gray-600">{technique.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Intensidad del cambio */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Intensidad del Cambio</Text>
          <View className="space-y-2">
            {intensities.map((intensity) => (
              <TouchableOpacity
                key={intensity.id}
                className={`p-3 rounded-lg border-2 ${
                  selectedIntensity === intensity.id
                    ? 'border-orange-500 bg-orange-50'
                    : 'border-gray-300'
                }`}
                onPress={() => setSelectedIntensity(intensity.id as DesiredColorData['intensity'])}
              >
                <Text className={`font-medium ${
                  selectedIntensity === intensity.id ? 'text-orange-800' : 'text-gray-800'
                }`}>
                  {intensity.name}
                </Text>
                <Text className="text-sm text-gray-600">{intensity.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Descripción del estilista */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Descripción Detallada *</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 min-h-[100px] bg-white text-gray-800"
            multiline
            placeholder="Describe detalladamente el resultado deseado: tono exacto, técnica específica, intensidad, zonas de aplicación, etc."
            value={stylistDescription}
            onChangeText={setStylistDescription}
            textAlignVertical="top"
          />
        </View>

        {/* Preferencias del cliente */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Preferencias del Cliente</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 min-h-[80px] bg-white text-gray-800"
            multiline
            placeholder="Comentarios o preferencias específicas del cliente..."
            value={clientPreferences}
            onChangeText={setClientPreferences}
            textAlignVertical="top"
          />
        </View>

        {/* Comparación con diagnóstico */}
        <View className="bg-yellow-50 rounded-lg p-4 mb-4 border border-yellow-200">
          <View className="flex-row items-center mb-2">
            <AlertTriangle size={16} color="#F59E0B" />
            <Text className="text-yellow-800 font-medium ml-2">Análisis de Viabilidad</Text>
          </View>
          <Text className="text-yellow-700 text-sm">
            Color actual: Nivel {diagnosis.roots.naturalLevel} ({diagnosis.roots.undertone}){'\n'}
            Color deseado: Nivel {colorAnalysis.targetLevel} ({colorAnalysis.targetUndertone}){'\n'}
            Cambio requerido: {Math.abs(colorAnalysis.targetLevel - diagnosis.roots.naturalLevel)} niveles
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Definición del Color Deseado
          </Text>
          <Text className="text-gray-600">
            Define el color objetivo con precisión usando IA
          </Text>
        </View>

        {currentStep === 'reference' && renderReferenceStep()}
        {currentStep === 'analysis' && renderAnalysisStep()}
        {currentStep === 'definition' && renderDefinitionStep()}
      </ScrollView>

      {/* Botones de acción */}
      {currentStep === 'reference' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={onBack}
            >
              <Text className="text-gray-800 font-semibold text-center">Volver</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-lg ${
                referenceImages.length > 0 ? 'bg-purple-500' : 'bg-gray-300'
              }`}
              onPress={analyzeReferences}
              disabled={referenceImages.length === 0}
            >
              <Text className="text-white font-semibold text-center">
                {referenceImages.length > 0
                  ? `Analizar ${referenceImages.length} Imagen${referenceImages.length > 1 ? 'es' : ''}`
                  : 'Añade Referencias'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {currentStep === 'definition' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={() => setCurrentStep('reference')}
            >
              <Text className="text-gray-800 font-semibold text-center">Modificar Referencias</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-lg ${
                stylistDescription.trim() ? 'bg-purple-500' : 'bg-gray-300'
              }`}
              onPress={handleDefinitionComplete}
              disabled={!stylistDescription.trim()}
            >
              <Text className="text-white font-semibold text-center">Confirmar Color Deseado</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default DesiredColorDefinition;

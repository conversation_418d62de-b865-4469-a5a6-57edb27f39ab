import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Dimensions,
  Animated,
} from "react-native";
import {
  Camera,
  CheckCircle,
  AlertTriangle,
  Eye,
  Droplets,
  Scissors,
  Shield,
  Zap,
  RefreshCw,
  Info,
  Target,
  Sun,
  Focus,
} from "lucide-react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { HairDiagnosisData, ImageMetadata } from "../../types/ColorConsultationTypes";

const { width, height } = Dimensions.get("window");

interface EnhancedHairDiagnosisProps {
  onDiagnosisComplete: (diagnosis: HairDiagnosisData) => void;
  onBack: () => void;
}

const EnhancedHairDiagnosis: React.FC<EnhancedHairDiagnosisProps> = ({
  onDiagnosisComplete,
  onBack,
}) => {
  const [currentStep, setCurrentStep] = useState<'capture' | 'analysis' | 'validation'>('capture');
  const [capturedImages, setCapturedImages] = useState<ImageMetadata[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showCaptureGuide, setShowCaptureGuide] = useState(false);
  const [selectedZone, setSelectedZone] = useState<ImageMetadata['zone'] | null>(null);
  const [aiDiagnosis, setAiDiagnosis] = useState<Partial<HairDiagnosisData> | null>(null);
  const [stylistValidation, setStylistValidation] = useState<any>({});
  
  const pulseAnim = new Animated.Value(1);

  useEffect(() => {
    startPulseAnimation();
  }, []);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const requiredZones: ImageMetadata['zone'][] = ['frontal', 'coronilla', 'nuca'];
  const optionalZones: ImageMetadata['zone'][] = ['lateral_izquierdo', 'lateral_derecho', 'detalle'];

  const getZoneDisplayName = (zone: ImageMetadata['zone']) => {
    const names = {
      frontal: 'Frontal',
      coronilla: 'Coronilla',
      nuca: 'Nuca',
      lateral_izquierdo: 'Lateral Izquierdo',
      lateral_derecho: 'Lateral Derecho',
      detalle: 'Detalle/Textura',
    };
    return names[zone];
  };

  const getZoneInstructions = (zone: ImageMetadata['zone']) => {
    const instructions = {
      frontal: 'Cabello peinado hacia atrás, vista frontal completa',
      coronilla: 'Vista superior de la coronilla, separando el cabello',
      nuca: 'Vista posterior de la nuca y parte baja del cabello',
      lateral_izquierdo: 'Perfil izquierdo, mostrando la caída natural',
      lateral_derecho: 'Perfil derecho, mostrando la caída natural',
      detalle: 'Acercamiento para mostrar textura y condición',
    };
    return instructions[zone];
  };

  const hasZoneImage = (zone: ImageMetadata['zone']) => {
    return capturedImages.some(img => img.zone === zone);
  };

  const canProceedToAnalysis = () => {
    return requiredZones.every(zone => hasZoneImage(zone));
  };

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permisos Requeridos',
        'Necesitamos acceso a la cámara para capturar imágenes del cabello.'
      );
      return false;
    }
    return true;
  };

  const simulateFaceBlurring = async (imageUri: string): Promise<string> => {
    // En una implementación real, aquí se haría la detección y difuminado de rostros
    // usando expo-face-detector, TensorFlow.js o una API externa
    console.log('Aplicando difuminado de rostro a:', imageUri);
    
    // Simular tiempo de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return imageUri; // En producción, retornaría la imagen con rostro difuminado
  };

  const compressImage = async (imageUri: string): Promise<{ uri: string; originalSize: number; compressedSize: number }> => {
    // En una implementación real, aquí se comprimiría la imagen
    // usando expo-image-manipulator
    console.log('Comprimiendo imagen:', imageUri);
    
    return {
      uri: imageUri,
      originalSize: 2048000, // 2MB simulado
      compressedSize: 512000, // 512KB simulado
    };
  };

  const analyzeImageQuality = (imageUri: string): { quality: ImageMetadata['quality']; lighting: ImageMetadata['lightingConditions'] } => {
    // En una implementación real, aquí se analizaría la calidad de la imagen
    // usando algoritmos de análisis de imagen
    
    // Simulación basada en condiciones aleatorias
    const qualities: ImageMetadata['quality'][] = ['excellent', 'good', 'acceptable'];
    const lightings: ImageMetadata['lightingConditions'][] = ['natural', 'artificial_good', 'mixed'];
    
    return {
      quality: qualities[Math.floor(Math.random() * qualities.length)],
      lighting: lightings[Math.floor(Math.random() * lightings.length)],
    };
  };

  const captureImage = async (zone: ImageMetadata['zone']) => {
    const hasPermissions = await requestPermissions();
    if (!hasPermissions) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const originalUri = result.assets[0].uri;
        
        // Mostrar indicador de procesamiento
        Alert.alert('Procesando...', 'Aplicando difuminado de rostro y optimizaciones');
        
        // Aplicar difuminado de rostro
        const blurredUri = await simulateFaceBlurring(originalUri);
        
        // Comprimir imagen
        const { uri: compressedUri, originalSize, compressedSize } = await compressImage(blurredUri);
        
        // Analizar calidad
        const { quality, lighting } = analyzeImageQuality(compressedUri);
        
        const imageMetadata: ImageMetadata = {
          id: `img_${Date.now()}_${zone}`,
          uri: compressedUri,
          zone,
          timestamp: new Date().toISOString(),
          quality,
          lightingConditions: lighting,
          faceBlurred: true,
          originalSize,
          compressedSize,
        };

        // Reemplazar imagen existente de la misma zona o agregar nueva
        const updatedImages = capturedImages.filter(img => img.zone !== zone);
        setCapturedImages([...updatedImages, imageMetadata]);

        Alert.alert(
          'Imagen Capturada',
          `Imagen de ${getZoneDisplayName(zone)} capturada exitosamente.\n\nCalidad: ${quality}\nIluminación: ${lighting}\nRostro difuminado: ✓\nCompresión: ${Math.round((1 - compressedSize/originalSize) * 100)}%`
        );
      }
    } catch (error) {
      console.error('Error capturando imagen:', error);
      Alert.alert('Error', 'No se pudo capturar la imagen. Inténtalo de nuevo.');
    }
  };

  const performAIAnalysis = async () => {
    setIsAnalyzing(true);
    setCurrentStep('analysis');

    try {
      // Simular tiempo de análisis IA (2-4 segundos)
      const analysisTime = 2000 + Math.random() * 2000;
      await new Promise(resolve => setTimeout(resolve, analysisTime));

      // Simular análisis IA exhaustivo
      const mockDiagnosis: Partial<HairDiagnosisData> = {
        roots: {
          naturalLevel: 4,
          undertone: 'warm',
          grayPercentage: 15,
          regrowth: 12,
          condition: 'normal',
        },
        midLengths: {
          level: 5,
          undertone: 'warm',
          porosity: 'medium',
          elasticity: 'good',
          condition: 'normal',
        },
        ends: {
          level: 6,
          porosity: 'high',
          condition: 'damaged',
          needsTrimming: true,
        },
        hairDiameter: 'medium',
        density: 'medium',
        texture: 'wavy',
        resistance: 'medium',
        chemicalHistory: [],
        clientInfo: {
          allergies: [],
          sensitivities: [],
          medications: [],
          scalpCondition: 'healthy',
          pregnancyStatus: 'unknown',
          previousReactions: [],
          lifestyle: {
            washFrequency: 'twice_weekly',
            heatStyling: 'occasional',
            swimmingExposure: 'never',
            sunExposure: 'moderate',
          },
        },
        images: capturedImages,
        aiAnalysis: {
          confidence: 85 + Math.random() * 10,
          processingTime: analysisTime / 1000,
          modelVersion: 'GPT-4-Vision-v1.2',
          analysisNotes: [
            'Análisis basado en ' + capturedImages.length + ' imágenes de alta calidad',
            'Detección automática de niveles por zonas',
            'Análisis de porosidad mediante textura visual',
            'Evaluación de condición general del cabello',
          ],
        },
        stylistValidation: {
          reviewed: false,
          modifications: [],
          finalApproval: false,
        },
      };

      setAiDiagnosis(mockDiagnosis);
      setCurrentStep('validation');

      Alert.alert(
        'Análisis Completado',
        `Análisis IA completado con ${Math.round(mockDiagnosis.aiAnalysis!.confidence)}% de confianza.\n\nRevisa los resultados y confirma antes de continuar.`
      );
    } catch (error) {
      console.error('Error en análisis IA:', error);
      Alert.alert('Error', 'Error durante el análisis IA. Inténtalo de nuevo.');
      setCurrentStep('capture');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderCaptureStep = () => (
    <View className="flex-1">
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="text-lg font-bold mb-2">Captura Multi-Imagen Avanzada</Text>
        <Text className="text-gray-600 mb-4">
          Captura imágenes desde diferentes ángulos para un análisis 360° completo
        </Text>
        
        <View className="bg-blue-50 p-3 rounded-lg border border-blue-200 mb-4">
          <View className="flex-row items-center mb-2">
            <Info size={16} color="#3B82F6" />
            <Text className="text-blue-800 font-medium ml-2">Guía de Captura Inteligente</Text>
          </View>
          <Text className="text-blue-700 text-sm">
            • Mínimo 3 imágenes requeridas (frontal, coronilla, nuca){'\n'}
            • Hasta 7 imágenes para análisis completo{'\n'}
            • Iluminación natural preferida{'\n'}
            • Rostros automáticamente difuminados{'\n'}
            • Compresión inteligente aplicada
          </Text>
        </View>

        <View className="flex-row items-center justify-between mb-4">
          <Text className="font-semibold text-gray-800">Progreso de Captura</Text>
          <View className="bg-blue-100 px-3 py-1 rounded-full">
            <Text className="text-blue-800 font-medium text-sm">
              {capturedImages.length}/7 imágenes
            </Text>
          </View>
        </View>
      </View>

      {/* Zonas requeridas */}
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Imágenes Requeridas</Text>
        <View className="space-y-3">
          {requiredZones.map(zone => (
            <TouchableOpacity
              key={zone}
              className={`p-3 rounded-lg border-2 ${
                hasZoneImage(zone) 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-red-300 bg-red-50'
              }`}
              onPress={() => captureImage(zone)}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  <View className="flex-row items-center">
                    {hasZoneImage(zone) ? (
                      <CheckCircle size={20} color="#10B981" />
                    ) : (
                      <Camera size={20} color="#EF4444" />
                    )}
                    <Text className={`font-semibold ml-2 ${
                      hasZoneImage(zone) ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {getZoneDisplayName(zone)}
                    </Text>
                  </View>
                  <Text className="text-sm text-gray-600 mt-1">
                    {getZoneInstructions(zone)}
                  </Text>
                </View>
                {hasZoneImage(zone) && (
                  <View className="w-12 h-12 rounded-lg overflow-hidden ml-3">
                    <Image
                      source={{ uri: capturedImages.find(img => img.zone === zone)?.uri }}
                      className="w-full h-full"
                      contentFit="cover"
                    />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Zonas opcionales */}
      <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
        <Text className="font-semibold text-gray-800 mb-3">Imágenes Opcionales (Recomendadas)</Text>
        <View className="space-y-3">
          {optionalZones.map(zone => (
            <TouchableOpacity
              key={zone}
              className={`p-3 rounded-lg border-2 ${
                hasZoneImage(zone) 
                  ? 'border-green-500 bg-green-50' 
                  : 'border-gray-300 bg-gray-50'
              }`}
              onPress={() => captureImage(zone)}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  <View className="flex-row items-center">
                    {hasZoneImage(zone) ? (
                      <CheckCircle size={20} color="#10B981" />
                    ) : (
                      <Camera size={20} color="#6B7280" />
                    )}
                    <Text className={`font-semibold ml-2 ${
                      hasZoneImage(zone) ? 'text-green-800' : 'text-gray-700'
                    }`}>
                      {getZoneDisplayName(zone)}
                    </Text>
                  </View>
                  <Text className="text-sm text-gray-600 mt-1">
                    {getZoneInstructions(zone)}
                  </Text>
                </View>
                {hasZoneImage(zone) && (
                  <View className="w-12 h-12 rounded-lg overflow-hidden ml-3">
                    <Image
                      source={{ uri: capturedImages.find(img => img.zone === zone)?.uri }}
                      className="w-full h-full"
                      contentFit="cover"
                    />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  const renderAnalysisStep = () => (
    <View className="flex-1 justify-center items-center p-4">
      <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
        <View className="bg-blue-500 w-24 h-24 rounded-full items-center justify-center mb-6">
          <Zap size={40} color="white" />
        </View>
      </Animated.View>

      <Text className="text-2xl font-bold text-gray-800 mb-2">Análisis IA en Progreso</Text>
      <Text className="text-gray-600 text-center mb-4">
        Analizando {capturedImages.length} imágenes con inteligencia artificial avanzada
      </Text>

      <View className="bg-white rounded-lg p-4 w-full max-w-sm">
        <Text className="font-semibold text-gray-800 mb-2">Procesando:</Text>
        <Text className="text-sm text-gray-600">• Detección de niveles naturales</Text>
        <Text className="text-sm text-gray-600">• Análisis de subtonos</Text>
        <Text className="text-sm text-gray-600">• Evaluación de porosidad</Text>
        <Text className="text-sm text-gray-600">• Condición del cabello</Text>
        <Text className="text-sm text-gray-600">• Análisis por zonas</Text>
      </View>
    </View>
  );

  const renderValidationStep = () => {
    if (!aiDiagnosis) return null;

    return (
      <View className="flex-1">
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <View className="flex-row items-center mb-3">
            <CheckCircle size={20} color="#10B981" />
            <Text className="text-lg font-bold text-green-800 ml-2">Análisis IA Completado</Text>
            <View className="bg-green-100 px-2 py-1 rounded-full ml-auto">
              <Text className="text-green-800 text-xs font-medium">
                {Math.round(aiDiagnosis.aiAnalysis?.confidence || 0)}% confianza
              </Text>
            </View>
          </View>

          <Text className="text-gray-600 mb-4">
            Revisa y valida los resultados del análisis antes de continuar
          </Text>
        </View>

        {/* Resultados por zonas */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Análisis por Zonas</Text>

          <View className="space-y-3">
            <View className="bg-gray-50 p-3 rounded-lg">
              <Text className="font-medium text-gray-800">Raíces</Text>
              <Text className="text-sm text-gray-600">
                Nivel: {aiDiagnosis.roots?.naturalLevel} | Subtono: {aiDiagnosis.roots?.undertone} | Canas: {aiDiagnosis.roots?.grayPercentage}%
              </Text>
            </View>

            <View className="bg-gray-50 p-3 rounded-lg">
              <Text className="font-medium text-gray-800">Medios</Text>
              <Text className="text-sm text-gray-600">
                Nivel: {aiDiagnosis.midLengths?.level} | Porosidad: {aiDiagnosis.midLengths?.porosity} | Elasticidad: {aiDiagnosis.midLengths?.elasticity}
              </Text>
            </View>

            <View className="bg-gray-50 p-3 rounded-lg">
              <Text className="font-medium text-gray-800">Puntas</Text>
              <Text className="text-sm text-gray-600">
                Nivel: {aiDiagnosis.ends?.level} | Porosidad: {aiDiagnosis.ends?.porosity} | Necesita corte: {aiDiagnosis.ends?.needsTrimming ? 'Sí' : 'No'}
              </Text>
            </View>
          </View>
        </View>

        {/* Características generales */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Características Generales</Text>

          <View className="grid grid-cols-2 gap-3">
            <View className="bg-blue-50 p-3 rounded-lg">
              <Text className="font-medium text-blue-800">Diámetro</Text>
              <Text className="text-sm text-blue-600">{aiDiagnosis.hairDiameter}</Text>
            </View>

            <View className="bg-blue-50 p-3 rounded-lg">
              <Text className="font-medium text-blue-800">Densidad</Text>
              <Text className="text-sm text-blue-600">{aiDiagnosis.density}</Text>
            </View>

            <View className="bg-blue-50 p-3 rounded-lg">
              <Text className="font-medium text-blue-800">Textura</Text>
              <Text className="text-sm text-blue-600">{aiDiagnosis.texture}</Text>
            </View>

            <View className="bg-blue-50 p-3 rounded-lg">
              <Text className="font-medium text-blue-800">Resistencia</Text>
              <Text className="text-sm text-blue-600">{aiDiagnosis.resistance}</Text>
            </View>
          </View>
        </View>

        {/* Notas del análisis IA */}
        <View className="bg-white rounded-lg p-4 mb-4 shadow-sm">
          <Text className="font-semibold text-gray-800 mb-3">Notas del Análisis IA</Text>
          {aiDiagnosis.aiAnalysis?.analysisNotes?.map((note, index) => (
            <Text key={index} className="text-sm text-gray-600 mb-1">• {note}</Text>
          ))}
        </View>
      </View>
    );
  };

  const handleValidationComplete = () => {
    if (!aiDiagnosis) return;

    const finalDiagnosis: HairDiagnosisData = {
      ...aiDiagnosis as HairDiagnosisData,
      stylistValidation: {
        reviewed: true,
        modifications: [],
        finalApproval: true,
        reviewTimestamp: new Date().toISOString(),
      },
    };

    onDiagnosisComplete(finalDiagnosis);
  };

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Diagnóstico Capilar Exhaustivo
          </Text>
          <Text className="text-gray-600">
            Análisis completo con IA para formulación precisa
          </Text>
        </View>

        {currentStep === 'capture' && renderCaptureStep()}
        {currentStep === 'analysis' && renderAnalysisStep()}
        {currentStep === 'validation' && renderValidationStep()}
      </ScrollView>

      {/* Botones de acción */}
      {currentStep === 'capture' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={onBack}
            >
              <Text className="text-gray-800 font-semibold text-center">Volver</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className={`flex-1 py-3 rounded-lg ${
                canProceedToAnalysis() ? 'bg-blue-500' : 'bg-gray-300'
              }`}
              onPress={performAIAnalysis}
              disabled={!canProceedToAnalysis()}
            >
              <Text className="text-white font-semibold text-center">
                {canProceedToAnalysis()
                  ? `Analizar ${capturedImages.length} Imágenes`
                  : 'Captura Imágenes Requeridas'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {currentStep === 'validation' && (
        <View className="bg-white p-4 border-t border-gray-100">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 rounded-lg"
              onPress={() => setCurrentStep('capture')}
            >
              <Text className="text-gray-800 font-semibold text-center">Recapturar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="flex-1 bg-blue-500 py-3 rounded-lg"
              onPress={handleValidationComplete}
            >
              <Text className="text-white font-semibold text-center">Confirmar Diagnóstico</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default EnhancedHairDiagnosis;

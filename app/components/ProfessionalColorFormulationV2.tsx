import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
} from "react-native";
import {
  ArrowLeft,
  ArrowRight,
  <PERSON>lette,
  Zap,
  CheckCircle,
  Alert<PERSON><PERSON>gle,
  <PERSON>,
  Star,
} from "lucide-react-native";
import { useRouter } from "expo-router";

// Tipos simplificados para uso real en salón
interface ColorFormulationData {
  currentColor: {
    colorId: string;
    isVirgin: boolean;
    hairCondition: number; // 1-5 scale
  };
  desiredColor: {
    colorId: string;
    customDescription?: string;
  };
  formulation: {
    brand: string;
    products: Array<{
      name: string;
      code: string;
      amount: string;
    }>;
    developer: {
      volume: number;
      ratio: string;
    };
    processingTime: number;
    sessionsRequired: number;
  };
}

const ProfessionalColorFormulationV2 = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<'current' | 'desired' | 'formulation'>('current');
  const [data, setData] = useState<ColorFormulationData>({
    currentColor: { colorId: '', isVirgin: true, hairCondition: 3 },
    desiredColor: { colorId: '' },
    formulation: {
      brand: '',
      products: [],
      developer: { volume: 20, ratio: '1:1' },
      processingTime: 30,
      sessionsRequired: 1,
    },
  });

  // Colores más comunes en salones profesionales
  const commonColors = [
    { id: 'black', name: 'Negro Natural', level: 1, hex: '#1a1a1a' },
    { id: 'dark-brown', name: 'Castaño Oscuro', level: 3, hex: '#3d2817' },
    { id: 'medium-brown', name: 'Castaño Medio', level: 4, hex: '#4a3728' },
    { id: 'light-brown', name: 'Castaño Claro', level: 5, hex: '#6b4423' },
    { id: 'dark-blonde', name: 'Rubio Oscuro', level: 6, hex: '#8b5a2b' },
    { id: 'medium-blonde', name: 'Rubio Medio', level: 7, hex: '#a67c52' },
    { id: 'light-blonde', name: 'Rubio Claro', level: 8, hex: '#c19a6b' },
    { id: 'very-light-blonde', name: 'Rubio Muy Claro', level: 9, hex: '#d4b896' },
    { id: 'platinum', name: 'Platino', level: 10, hex: '#e8d5c4' },
    { id: 'red', name: 'Rojizo', level: 6, hex: '#8B4513' },
    { id: 'copper', name: 'Cobrizo', level: 7, hex: '#B87333' },
    { id: 'ash', name: 'Ceniza', level: 7, hex: '#A0A0A0' },
  ];

  // Colores objetivo populares con nombres comerciales
  const popularTargetColors = [
    { id: 'honey-blonde', name: 'Rubio Miel', description: 'Cálido y luminoso', hex: '#DAA520' },
    { id: 'ash-blonde', name: 'Rubio Ceniza', description: 'Frío y sofisticado', hex: '#C0C0C0' },
    { id: 'chocolate', name: 'Chocolate', description: 'Rico y profundo', hex: '#7B3F00' },
    { id: 'caramel', name: 'Caramelo', description: 'Dulce y cálido', hex: '#AF6E4D' },
    { id: 'platinum-ice', name: 'Platino Hielo', description: 'Ultra claro y frío', hex: '#E5E4E2' },
    { id: 'copper-red', name: 'Cobrizo Intenso', description: 'Vibrante y llamativo', hex: '#B87333' },
    { id: 'espresso', name: 'Espresso', description: 'Oscuro y rico', hex: '#3C2415' },
    { id: 'golden-brown', name: 'Castaño Dorado', description: 'Cálido y natural', hex: '#996515' },
    { id: 'rose-gold', name: 'Oro Rosa', description: 'Tendencia moderna', hex: '#E8B4B8' },
    { id: 'balayage-natural', name: 'Balayage Natural', description: 'Degradado sutil', hex: '#D2B48C' },
    { id: 'ombre-brown', name: 'Ombré Castaño', description: 'Transición gradual', hex: '#8B4513' },
    { id: 'highlights-blonde', name: 'Mechas Rubias', description: 'Luminosidad natural', hex: '#F5DEB3' },
  ];

  // Marcas profesionales más usadas
  const professionalBrands = [
    { id: 'loreal', name: "L'Oréal Professionnel", popular: true },
    { id: 'wella', name: 'Wella Professionals', popular: true },
    { id: 'schwarzkopf', name: 'Schwarzkopf Professional', popular: true },
    { id: 'matrix', name: 'Matrix', popular: false },
    { id: 'redken', name: 'Redken', popular: false },
    { id: 'goldwell', name: 'Goldwell', popular: false },
  ];

  const updateCurrentColor = (field: string, value: any) => {
    setData(prev => ({
      ...prev,
      currentColor: { ...prev.currentColor, [field]: value }
    }));
  };

  const updateDesiredColor = (field: string, value: any) => {
    setData(prev => ({
      ...prev,
      desiredColor: { ...prev.desiredColor, [field]: value }
    }));
  };

  const calculateFormulation = () => {
    if (!data.currentColor.colorId || !data.desiredColor.colorId || !data.formulation.brand) {
      return;
    }

    const currentColorData = commonColors.find(c => c.id === data.currentColor.colorId);
    const desiredColorData = popularTargetColors.find(c => c.id === data.desiredColor.colorId);
    
    if (!currentColorData || !desiredColorData) return;

    // Simulación de cálculo profesional
    const levelDifference = 8 - currentColorData.level; // Asumiendo nivel objetivo 8
    const sessionsRequired = levelDifference > 3 ? 2 : 1;
    const processingTime = levelDifference > 2 ? 45 : 30;
    const developerVolume = levelDifference > 2 ? 30 : 20;

    // Productos simulados según la marca
    const products = [
      { name: 'Colorante Base', code: '8.0', amount: '60ml' },
      { name: 'Matizador', code: '8.1', amount: '15ml' },
    ];

    setData(prev => ({
      ...prev,
      formulation: {
        ...prev.formulation,
        products,
        developer: { volume: developerVolume, ratio: '1:1' },
        processingTime,
        sessionsRequired,
      }
    }));
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case 'current':
        return data.currentColor.colorId !== '';
      case 'desired':
        return data.desiredColor.colorId !== '';
      case 'formulation':
        return data.formulation.brand !== '';
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep === 'current') {
      setCurrentStep('desired');
    } else if (currentStep === 'desired') {
      setCurrentStep('formulation');
      calculateFormulation();
    }
  };

  const handleBack = () => {
    if (currentStep === 'desired') {
      setCurrentStep('current');
    } else if (currentStep === 'formulation') {
      setCurrentStep('desired');
    } else {
      router.back();
    }
  };

  const renderStepIndicator = () => (
    <View className="flex-row justify-center items-center py-4 bg-white border-b border-gray-200">
      <View className={`w-8 h-8 rounded-full items-center justify-center ${
        currentStep === 'current' ? 'bg-blue-600' : 'bg-green-500'
      }`}>
        <Text className="text-white font-bold text-sm">1</Text>
      </View>
      <View className={`w-16 h-1 ${currentStep !== 'current' ? 'bg-green-500' : 'bg-gray-300'}`} />
      
      <View className={`w-8 h-8 rounded-full items-center justify-center ${
        currentStep === 'desired' ? 'bg-blue-600' : 
        currentStep === 'formulation' ? 'bg-green-500' : 'bg-gray-300'
      }`}>
        <Text className={`font-bold text-sm ${
          currentStep === 'current' ? 'text-gray-500' : 'text-white'
        }`}>2</Text>
      </View>
      <View className={`w-16 h-1 ${currentStep === 'formulation' ? 'bg-green-500' : 'bg-gray-300'}`} />
      
      <View className={`w-8 h-8 rounded-full items-center justify-center ${
        currentStep === 'formulation' ? 'bg-blue-600' : 'bg-gray-300'
      }`}>
        <Text className={`font-bold text-sm ${
          currentStep === 'formulation' ? 'text-white' : 'text-gray-500'
        }`}>3</Text>
      </View>
    </View>
  );

  const renderCurrentColorStep = () => (
    <ScrollView className="flex-1 p-4">
      <Text className="text-2xl font-bold text-gray-800 mb-2">
        Color Actual del Cliente
      </Text>
      <Text className="text-gray-600 mb-6">
        Selecciona el color que más se aproxime
      </Text>

      <View className="flex-row flex-wrap justify-between mb-6">
        {commonColors.map((color) => (
          <TouchableOpacity
            key={color.id}
            onPress={() => updateCurrentColor('colorId', color.id)}
            className={`w-[30%] mb-4 ${
              data.currentColor.colorId === color.id ? 'opacity-100' : 'opacity-70'
            }`}
          >
            <View
              className={`aspect-square rounded-lg border-4 ${
                data.currentColor.colorId === color.id ? 'border-blue-500' : 'border-gray-300'
              }`}
              style={{ backgroundColor: color.hex }}
            />
            <Text className="text-center text-sm font-medium mt-2 text-gray-800">
              {color.name}
            </Text>
            <Text className="text-center text-xs text-gray-500">
              Nivel {color.level}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View className="mb-6">
        <Text className="text-lg font-semibold text-gray-800 mb-3">
          Estado del Cabello
        </Text>
        
        <View className="flex-row justify-between mb-4">
          <TouchableOpacity
            onPress={() => updateCurrentColor('isVirgin', true)}
            className={`flex-1 p-4 rounded-lg border mr-2 ${
              data.currentColor.isVirgin ? 'border-green-500 bg-green-50' : 'border-gray-300 bg-white'
            }`}
          >
            <Text className={`text-center font-semibold ${
              data.currentColor.isVirgin ? 'text-green-800' : 'text-gray-700'
            }`}>
              Cabello Virgen
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => updateCurrentColor('isVirgin', false)}
            className={`flex-1 p-4 rounded-lg border ml-2 ${
              !data.currentColor.isVirgin ? 'border-orange-500 bg-orange-50' : 'border-gray-300 bg-white'
            }`}
          >
            <Text className={`text-center font-semibold ${
              !data.currentColor.isVirgin ? 'text-orange-800' : 'text-gray-700'
            }`}>
              Previamente Tratado
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );

  const renderDesiredColorStep = () => (
    <ScrollView className="flex-1 p-4">
      <Text className="text-2xl font-bold text-gray-800 mb-2">
        Color Deseado
      </Text>
      <Text className="text-gray-600 mb-6">
        ¿Qué resultado busca el cliente?
      </Text>

      <View className="flex-row flex-wrap justify-between mb-6">
        {popularTargetColors.map((color) => (
          <TouchableOpacity
            key={color.id}
            onPress={() => updateDesiredColor('colorId', color.id)}
            className={`w-[48%] mb-4 ${
              data.desiredColor.colorId === color.id ? 'opacity-100' : 'opacity-70'
            }`}
          >
            <View
              className={`aspect-square rounded-lg border-4 ${
                data.desiredColor.colorId === color.id ? 'border-purple-500' : 'border-gray-300'
              }`}
              style={{ backgroundColor: color.hex }}
            />
            <Text className="text-center text-sm font-bold mt-2 text-gray-800">
              {color.name}
            </Text>
            <Text className="text-center text-xs text-gray-500">
              {color.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  const renderFormulationStep = () => (
    <ScrollView className="flex-1 p-4">
      <Text className="text-2xl font-bold text-gray-800 mb-2">
        Formulación Profesional
      </Text>
      <Text className="text-gray-600 mb-6">
        Selecciona tu marca preferida
      </Text>

      {/* Selector de Marca */}
      <View className="mb-6">
        <Text className="text-lg font-semibold text-gray-800 mb-3">
          Marca Profesional
        </Text>
        {professionalBrands.map((brand) => (
          <TouchableOpacity
            key={brand.id}
            onPress={() => setData(prev => ({ ...prev, formulation: { ...prev.formulation, brand: brand.id } }))}
            className={`p-4 rounded-lg border mb-2 flex-row items-center justify-between ${
              data.formulation.brand === brand.id ? 'border-green-500 bg-green-50' : 'border-gray-300 bg-white'
            }`}
          >
            <View className="flex-row items-center">
              <Text className={`font-semibold ${
                data.formulation.brand === brand.id ? 'text-green-800' : 'text-gray-800'
              }`}>
                {brand.name}
              </Text>
              {brand.popular && (
                <View className="bg-yellow-100 px-2 py-1 rounded-full ml-2">
                  <Text className="text-yellow-800 text-xs font-medium">Popular</Text>
                </View>
              )}
            </View>
            {data.formulation.brand === brand.id && (
              <CheckCircle size={20} color="#10B981" />
            )}
          </TouchableOpacity>
        ))}
      </View>

      {/* Resultado de Formulación */}
      {data.formulation.brand && data.formulation.products.length > 0 && (
        <View className="bg-green-50 p-4 rounded-lg border border-green-200 mb-6">
          <Text className="text-green-800 font-bold text-lg mb-3">
            ✅ Fórmula Calculada
          </Text>

          <View className="mb-4">
            <Text className="font-semibold text-gray-800 mb-2">Productos:</Text>
            {data.formulation.products.map((product, index) => (
              <Text key={index} className="text-gray-700">
                • {product.name} ({product.code}): {product.amount}
              </Text>
            ))}
          </View>

          <View className="mb-4">
            <Text className="font-semibold text-gray-800 mb-1">Oxidante:</Text>
            <Text className="text-gray-700">
              {data.formulation.developer.volume} vol. - Proporción {data.formulation.developer.ratio}
            </Text>
          </View>

          <View className="flex-row justify-between">
            <View>
              <Text className="font-semibold text-gray-800">Tiempo:</Text>
              <Text className="text-gray-700">{data.formulation.processingTime} min</Text>
            </View>
            <View>
              <Text className="font-semibold text-gray-800">Sesiones:</Text>
              <Text className="text-gray-700">{data.formulation.sessionsRequired}</Text>
            </View>
          </View>

          {data.formulation.sessionsRequired > 1 && (
            <View className="bg-yellow-100 p-3 rounded-lg mt-4 border border-yellow-300">
              <View className="flex-row items-center mb-1">
                <AlertTriangle size={16} color="#D97706" />
                <Text className="text-yellow-800 font-semibold ml-2">Atención</Text>
              </View>
              <Text className="text-yellow-700 text-sm">
                Se requieren {data.formulation.sessionsRequired} sesiones para lograr el resultado deseado de forma segura.
              </Text>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <StatusBar barStyle="dark-content" backgroundColor="#F9FAFB" />
      
      {/* Header */}
      <View className="bg-white px-4 py-3 shadow-sm border-b border-gray-100">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={handleBack} className="flex-row items-center">
            <ArrowLeft size={24} color="#374151" />
            <Text className="text-gray-800 font-semibold ml-2">
              {currentStep === 'current' ? 'Volver' : 'Anterior'}
            </Text>
          </TouchableOpacity>
          
          <View className="bg-purple-100 px-3 py-1 rounded-full">
            <Text className="text-purple-800 font-semibold text-sm">
              🎨 FORMULACIÓN EXPRESS
            </Text>
          </View>
        </View>
      </View>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Content */}
      {currentStep === 'current' && renderCurrentColorStep()}
      {currentStep === 'desired' && renderDesiredColorStep()}
      {currentStep === 'formulation' && renderFormulationStep()}

      {/* Navigation */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          onPress={handleNext}
          disabled={!canProceedToNext()}
          className={`flex-row items-center justify-center p-4 rounded-lg ${
            canProceedToNext() ? 'bg-blue-600' : 'bg-gray-300'
          }`}
        >
          <Text className={`font-semibold mr-2 ${
            canProceedToNext() ? 'text-white' : 'text-gray-500'
          }`}>
            {currentStep === 'formulation' ? 'Generar Fórmula' : 'Continuar'}
          </Text>
          <ArrowRight size={20} color={canProceedToNext() ? '#fff' : '#6B7280'} />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ProfessionalColorFormulationV2;

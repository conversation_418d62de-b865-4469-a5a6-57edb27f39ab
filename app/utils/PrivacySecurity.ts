// Sistema de privacidad y seguridad para el módulo de consulta de color
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface PrivacySettings {
  faceBlurringEnabled: boolean;
  dataRetentionDays: number;
  autoDeleteEnabled: boolean;
  encryptionEnabled: boolean;
  anonymizeData: boolean;
  shareWithThirdParties: boolean;
}

export interface SecurityAuditLog {
  id: string;
  timestamp: string;
  action: string;
  userId: string;
  clientId?: string;
  details: string;
  ipAddress?: string;
  deviceInfo?: string;
}

export class PrivacyManager {
  private static instance: PrivacyManager;
  private settings: PrivacySettings;
  private auditLogs: SecurityAuditLog[] = [];

  private constructor() {
    this.settings = {
      faceBlurringEnabled: true,
      dataRetentionDays: 365,
      autoDeleteEnabled: true,
      encryptionEnabled: true,
      anonymizeData: false,
      shareWithThirdParties: false,
    };
  }

  static getInstance(): PrivacyManager {
    if (!PrivacyManager.instance) {
      PrivacyManager.instance = new PrivacyManager();
    }
    return PrivacyManager.instance;
  }

  // Configuración de privacidad
  async loadPrivacySettings(): Promise<PrivacySettings> {
    try {
      const stored = await AsyncStorage.getItem('privacy_settings');
      if (stored) {
        this.settings = { ...this.settings, ...JSON.parse(stored) };
      }
      return this.settings;
    } catch (error) {
      console.error('Error cargando configuración de privacidad:', error);
      return this.settings;
    }
  }

  async updatePrivacySettings(newSettings: Partial<PrivacySettings>): Promise<void> {
    try {
      this.settings = { ...this.settings, ...newSettings };
      await AsyncStorage.setItem('privacy_settings', JSON.stringify(this.settings));
      
      await this.logSecurityEvent('privacy_settings_updated', 'system', {
        changes: Object.keys(newSettings).join(', '),
      });
    } catch (error) {
      console.error('Error actualizando configuración de privacidad:', error);
      throw error;
    }
  }

  getPrivacySettings(): PrivacySettings {
    return { ...this.settings };
  }

  // Detección y difuminado de rostros
  async detectAndBlurFaces(imageUri: string): Promise<{
    processedImageUri: string;
    facesDetected: number;
    processingTime: number;
  }> {
    const startTime = Date.now();
    
    try {
      // En una implementación real, usar expo-face-detector o TensorFlow.js
      console.log('Iniciando detección de rostros para:', imageUri);
      
      // Simular detección de rostros
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const facesDetected = Math.floor(Math.random() * 2); // 0-1 rostros simulados
      const processingTime = Date.now() - startTime;
      
      if (facesDetected > 0 && this.settings.faceBlurringEnabled) {
        // Simular difuminado
        console.log(`Difuminando ${facesDetected} rostro(s) detectado(s)`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      await this.logSecurityEvent('face_detection_completed', 'system', {
        facesDetected: facesDetected.toString(),
        processingTime: processingTime.toString(),
        imageUri: this.anonymizeUri(imageUri),
      });

      return {
        processedImageUri: imageUri, // En producción, retornaría la imagen procesada
        facesDetected,
        processingTime,
      };
    } catch (error) {
      console.error('Error en detección/difuminado de rostros:', error);
      
      await this.logSecurityEvent('face_detection_error', 'system', {
        error: error.toString(),
        imageUri: this.anonymizeUri(imageUri),
      });
      
      throw error;
    }
  }

  // Encriptación de datos sensibles
  async encryptSensitiveData(data: any): Promise<string> {
    if (!this.settings.encryptionEnabled) {
      return JSON.stringify(data);
    }

    try {
      // En una implementación real, usar una biblioteca de encriptación robusta
      const jsonData = JSON.stringify(data);

      // Simular hash simple para desarrollo
      const hash = this.simpleHash(jsonData + 'encryption_key');

      // Simular encriptación (en producción, usar AES o similar)
      const encryptedData = btoa(jsonData + '::' + hash);

      await this.logSecurityEvent('data_encrypted', 'system', {
        dataSize: jsonData.length.toString(),
      });

      return encryptedData;
    } catch (error) {
      console.error('Error encriptando datos:', error);
      throw error;
    }
  }

  async decryptSensitiveData(encryptedData: string): Promise<any> {
    if (!this.settings.encryptionEnabled) {
      return JSON.parse(encryptedData);
    }

    try {
      // Simular desencriptación
      const decodedData = atob(encryptedData);
      const [jsonData, hash] = decodedData.split('::');

      // Verificar integridad con hash simple
      const expectedHash = this.simpleHash(jsonData + 'encryption_key');

      if (hash !== expectedHash) {
        throw new Error('Datos comprometidos - hash no coincide');
      }

      await this.logSecurityEvent('data_decrypted', 'system', {
        dataSize: jsonData.length.toString(),
      });

      return JSON.parse(jsonData);
    } catch (error) {
      console.error('Error desencriptando datos:', error);
      await this.logSecurityEvent('decryption_error', 'system', {
        error: error.toString(),
      });
      throw error;
    }
  }

  // Anonimización de datos
  anonymizeClientData(clientData: any): any {
    if (!this.settings.anonymizeData) {
      return clientData;
    }

    const anonymized = { ...clientData };
    
    // Anonimizar campos sensibles
    if (anonymized.name) {
      anonymized.name = this.anonymizeName(anonymized.name);
    }
    if (anonymized.email) {
      anonymized.email = this.anonymizeEmail(anonymized.email);
    }
    if (anonymized.phone) {
      anonymized.phone = this.anonymizePhone(anonymized.phone);
    }
    
    return anonymized;
  }

  private anonymizeName(name: string): string {
    const parts = name.split(' ');
    return parts.map(part => part.charAt(0) + '*'.repeat(part.length - 1)).join(' ');
  }

  private anonymizeEmail(email: string): string {
    const [local, domain] = email.split('@');
    const anonymizedLocal = local.charAt(0) + '*'.repeat(local.length - 1);
    return `${anonymizedLocal}@${domain}`;
  }

  private anonymizePhone(phone: string): string {
    return phone.replace(/\d(?=\d{4})/g, '*');
  }

  private anonymizeUri(uri: string): string {
    // Mantener solo los últimos 8 caracteres para identificación
    return '***' + uri.slice(-8);
  }

  // Gestión de retención de datos
  async cleanupExpiredData(): Promise<{
    deletedConsultations: number;
    deletedImages: number;
    freedSpace: number;
  }> {
    if (!this.settings.autoDeleteEnabled) {
      return { deletedConsultations: 0, deletedImages: 0, freedSpace: 0 };
    }

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.settings.dataRetentionDays);
      
      // En una implementación real, consultar la base de datos
      const expiredConsultations = await this.findExpiredConsultations(cutoffDate);
      const expiredImages = await this.findExpiredImages(cutoffDate);
      
      let deletedConsultations = 0;
      let deletedImages = 0;
      let freedSpace = 0;
      
      // Simular limpieza
      for (const consultation of expiredConsultations) {
        await this.secureDeleteConsultation(consultation.id);
        deletedConsultations++;
        freedSpace += consultation.estimatedSize || 0;
      }
      
      for (const image of expiredImages) {
        await this.secureDeleteImage(image.uri);
        deletedImages++;
        freedSpace += image.size || 0;
      }

      await this.logSecurityEvent('data_cleanup_completed', 'system', {
        deletedConsultations: deletedConsultations.toString(),
        deletedImages: deletedImages.toString(),
        freedSpace: freedSpace.toString(),
      });

      return { deletedConsultations, deletedImages, freedSpace };
    } catch (error) {
      console.error('Error en limpieza de datos:', error);
      await this.logSecurityEvent('data_cleanup_error', 'system', {
        error: error.toString(),
      });
      throw error;
    }
  }

  private async findExpiredConsultations(cutoffDate: Date): Promise<any[]> {
    // Simular búsqueda de consultas expiradas
    return [];
  }

  private async findExpiredImages(cutoffDate: Date): Promise<any[]> {
    // Simular búsqueda de imágenes expiradas
    return [];
  }

  private async secureDeleteConsultation(consultationId: string): Promise<void> {
    // Implementar borrado seguro de consulta
    await AsyncStorage.removeItem(`consultation_${consultationId}`);
  }

  private async secureDeleteImage(imageUri: string): Promise<void> {
    // Implementar borrado seguro de imagen
    console.log('Borrado seguro de imagen:', this.anonymizeUri(imageUri));
  }

  // Función auxiliar para hash simple
  private simpleHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(36);
  }

  // Función auxiliar para generar UUID simple
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Auditoría de seguridad
  async logSecurityEvent(
    action: string,
    userId: string,
    details: Record<string, string> = {},
    clientId?: string
  ): Promise<void> {
    const logEntry: SecurityAuditLog = {
      id: this.generateUUID(),
      timestamp: new Date().toISOString(),
      action,
      userId,
      clientId,
      details: JSON.stringify(details),
      // En producción, obtener IP y device info reales
      ipAddress: '127.0.0.1',
      deviceInfo: 'Mobile App',
    };

    this.auditLogs.push(logEntry);
    
    // Mantener solo los últimos 1000 logs en memoria
    if (this.auditLogs.length > 1000) {
      this.auditLogs = this.auditLogs.slice(-1000);
    }

    // En producción, enviar a servidor de auditoría
    await this.persistAuditLog(logEntry);
  }

  private async persistAuditLog(logEntry: SecurityAuditLog): Promise<void> {
    try {
      const existingLogs = await AsyncStorage.getItem('security_audit_logs');
      const logs = existingLogs ? JSON.parse(existingLogs) : [];
      logs.push(logEntry);
      
      // Mantener solo los últimos 100 logs en storage
      const recentLogs = logs.slice(-100);
      await AsyncStorage.setItem('security_audit_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Error persistiendo log de auditoría:', error);
    }
  }

  async getAuditLogs(limit: number = 50): Promise<SecurityAuditLog[]> {
    try {
      const stored = await AsyncStorage.getItem('security_audit_logs');
      const logs = stored ? JSON.parse(stored) : [];
      return logs.slice(-limit).reverse(); // Más recientes primero
    } catch (error) {
      console.error('Error obteniendo logs de auditoría:', error);
      return [];
    }
  }

  // Validación de cumplimiento GDPR/LOPD
  async validateGDPRCompliance(): Promise<{
    compliant: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues = [];
    const recommendations = [];

    if (!this.settings.faceBlurringEnabled) {
      issues.push('Difuminado de rostros deshabilitado');
      recommendations.push('Habilitar difuminado automático de rostros');
    }

    if (!this.settings.encryptionEnabled) {
      issues.push('Encriptación de datos deshabilitada');
      recommendations.push('Habilitar encriptación de datos sensibles');
    }

    if (this.settings.dataRetentionDays > 365) {
      issues.push('Período de retención excesivo');
      recommendations.push('Reducir período de retención a máximo 1 año');
    }

    if (this.settings.shareWithThirdParties) {
      issues.push('Compartir con terceros habilitado');
      recommendations.push('Revisar políticas de compartir datos');
    }

    return {
      compliant: issues.length === 0,
      issues,
      recommendations,
    };
  }

  // Exportación de datos del cliente (derecho GDPR)
  async exportClientData(clientId: string): Promise<{
    personalData: any;
    consultations: any[];
    images: any[];
    auditLogs: SecurityAuditLog[];
  }> {
    try {
      await this.logSecurityEvent('data_export_requested', 'system', {
        clientId,
      });

      // En una implementación real, consultar todas las fuentes de datos
      const personalData = {}; // Datos personales del cliente
      const consultations = []; // Consultas del cliente
      const images = []; // Imágenes del cliente
      const auditLogs = this.auditLogs.filter(log => log.clientId === clientId);

      await this.logSecurityEvent('data_export_completed', 'system', {
        clientId,
        consultationsCount: consultations.length.toString(),
        imagesCount: images.length.toString(),
      });

      return {
        personalData,
        consultations,
        images,
        auditLogs,
      };
    } catch (error) {
      console.error('Error exportando datos del cliente:', error);
      await this.logSecurityEvent('data_export_error', 'system', {
        clientId,
        error: error.toString(),
      });
      throw error;
    }
  }

  // Eliminación completa de datos del cliente (derecho al olvido GDPR)
  async deleteAllClientData(clientId: string): Promise<void> {
    try {
      await this.logSecurityEvent('data_deletion_requested', 'system', {
        clientId,
      });

      // En una implementación real, eliminar de todas las fuentes
      // - Datos personales
      // - Consultas
      // - Imágenes
      // - Referencias en logs (anonimizar)

      await this.logSecurityEvent('data_deletion_completed', 'system', {
        clientId,
      });
    } catch (error) {
      console.error('Error eliminando datos del cliente:', error);
      await this.logSecurityEvent('data_deletion_error', 'system', {
        clientId,
        error: error.toString(),
      });
      throw error;
    }
  }
}

export default PrivacyManager;

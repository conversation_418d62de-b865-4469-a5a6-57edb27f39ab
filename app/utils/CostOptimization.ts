// Utilidades para optimización de costes y UX en el módulo de consulta de color
import { CostOptimizationConfig } from "../types/ColorConsultationTypes";

export const DEFAULT_COST_CONFIG: CostOptimizationConfig = {
  maxImagesPerPhase: {
    diagnosis: 7,
    reference: 3,
    result: 4,
  },
  imageCompression: {
    quality: 0.8,
    maxWidth: 1024,
    maxHeight: 1024,
  },
  aiPrompts: {
    diagnosis: `Analiza estas imágenes de cabello y proporciona un diagnóstico detallado incluyendo:
- Nivel natural (1-10)
- Subtono (warm/cool/neutral)
- Porosidad por zonas (low/medium/high)
- Condición general (healthy/normal/damaged/severely_damaged)
- Porcentaje de canas
- Análisis por zonas (raíces, medios, puntas)
- Diámetro, densidad y textura
- Recomendaciones específicas

Responde en formato JSON estructurado.`,
    
    colorAnalysis: `Analiza estas imágenes de referencia de color y extrae:
- Nivel de color objetivo (1-10)
- Subtono dominante (warm/cool/neutral)
- Reflejos principales
- Técnica aparente utilizada
- Luminosidad y saturación
- Viabilidad considerando el diagnóstico previo

Responde en formato JSON estructurado.`,
    
    formulation: `Basándote en el diagnóstico capilar y color deseado, genera una fórmula profesional incluyendo:
- Productos específicos con códigos
- Proporciones exactas
- Volumen de desarrollador
- Tiempo de procesamiento
- Instrucciones paso a paso
- Análisis de riesgo y advertencias
- Tratamientos pre/post recomendados
- Estimación de sesiones necesarias

Considera las marcas disponibles y responde en formato JSON estructurado.`,
  },
  modelSelection: {
    primary: 'gpt-4-vision-preview',
    fallback: 'gpt-4-turbo',
    economical: 'gpt-3.5-turbo',
  },
};

export class CostOptimizer {
  private config: CostOptimizationConfig;
  private usageStats: {
    imagesProcessed: number;
    tokensUsed: number;
    apiCalls: number;
    estimatedCost: number;
  };

  constructor(config: CostOptimizationConfig = DEFAULT_COST_CONFIG) {
    this.config = config;
    this.usageStats = {
      imagesProcessed: 0,
      tokensUsed: 0,
      apiCalls: 0,
      estimatedCost: 0,
    };
  }

  // Optimización de imágenes
  async optimizeImage(imageUri: string, phase: keyof CostOptimizationConfig['maxImagesPerPhase']): Promise<{
    uri: string;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  }> {
    try {
      // En una implementación real, usar expo-image-manipulator
      const { quality, maxWidth, maxHeight } = this.config.imageCompression;
      
      // Simular compresión
      const originalSize = 2048000; // 2MB simulado
      const compressedSize = Math.round(originalSize * quality);
      const compressionRatio = (1 - compressedSize / originalSize) * 100;

      console.log(`Imagen optimizada para fase ${phase}:`, {
        quality,
        maxWidth,
        maxHeight,
        compressionRatio: `${compressionRatio.toFixed(1)}%`,
      });

      this.usageStats.imagesProcessed++;

      return {
        uri: imageUri, // En producción, retornaría la URI de la imagen comprimida
        originalSize,
        compressedSize,
        compressionRatio,
      };
    } catch (error) {
      console.error('Error optimizando imagen:', error);
      throw error;
    }
  }

  // Selección inteligente de modelo IA
  selectOptimalModel(complexity: 'low' | 'medium' | 'high', fallbackMode: boolean = false): string {
    if (fallbackMode) {
      return this.config.modelSelection.fallback;
    }

    switch (complexity) {
      case 'low':
        return this.config.modelSelection.economical;
      case 'medium':
        return this.config.modelSelection.fallback;
      case 'high':
        return this.config.modelSelection.primary;
      default:
        return this.config.modelSelection.primary;
    }
  }

  // Optimización de prompts
  getOptimizedPrompt(type: keyof CostOptimizationConfig['aiPrompts'], context?: any): string {
    const basePrompt = this.config.aiPrompts[type];
    
    // Añadir contexto específico para reducir iteraciones
    let optimizedPrompt = basePrompt;
    
    if (context) {
      switch (type) {
        case 'diagnosis':
          if (context.imageCount) {
            optimizedPrompt += `\n\nAnaliza ${context.imageCount} imágenes proporcionadas.`;
          }
          break;
        case 'colorAnalysis':
          if (context.currentLevel) {
            optimizedPrompt += `\n\nNivel actual del cabello: ${context.currentLevel}`;
          }
          break;
        case 'formulation':
          if (context.preferredBrands) {
            optimizedPrompt += `\n\nMarcas disponibles: ${context.preferredBrands.join(', ')}`;
          }
          break;
      }
    }

    return optimizedPrompt;
  }

  // Validación de límites de imágenes
  validateImageLimit(phase: keyof CostOptimizationConfig['maxImagesPerPhase'], currentCount: number): {
    allowed: boolean;
    maxAllowed: number;
    remaining: number;
  } {
    const maxAllowed = this.config.maxImagesPerPhase[phase];
    const remaining = Math.max(0, maxAllowed - currentCount);
    
    return {
      allowed: currentCount < maxAllowed,
      maxAllowed,
      remaining,
    };
  }

  // Estimación de costes
  estimateAPICall(model: string, inputTokens: number, outputTokens: number): number {
    // Precios simulados por 1K tokens (actualizar con precios reales)
    const pricing = {
      'gpt-4-vision-preview': { input: 0.01, output: 0.03 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-3.5-turbo': { input: 0.0015, output: 0.002 },
    };

    const modelPricing = pricing[model as keyof typeof pricing] || pricing['gpt-4-turbo'];
    const inputCost = (inputTokens / 1000) * modelPricing.input;
    const outputCost = (outputTokens / 1000) * modelPricing.output;
    
    const totalCost = inputCost + outputCost;
    this.usageStats.estimatedCost += totalCost;
    this.usageStats.tokensUsed += inputTokens + outputTokens;
    this.usageStats.apiCalls++;

    return totalCost;
  }

  // Estadísticas de uso
  getUsageStats() {
    return { ...this.usageStats };
  }

  // Reset de estadísticas
  resetStats() {
    this.usageStats = {
      imagesProcessed: 0,
      tokensUsed: 0,
      apiCalls: 0,
      estimatedCost: 0,
    };
  }

  // Recomendaciones de optimización
  getOptimizationRecommendations(): string[] {
    const recommendations = [];
    
    if (this.usageStats.imagesProcessed > 15) {
      recommendations.push('Considera reducir el número de imágenes por consulta');
    }
    
    if (this.usageStats.estimatedCost > 5) {
      recommendations.push('Coste elevado detectado - revisa la configuración de modelos');
    }
    
    if (this.usageStats.apiCalls > 10) {
      recommendations.push('Múltiples llamadas API - considera agrupar análisis');
    }

    return recommendations;
  }
}

// Utilidades para UX
export class UXOptimizer {
  // Feedback en tiempo real para calidad de imagen
  static analyzeImageQuality(imageUri: string): {
    quality: 'excellent' | 'good' | 'acceptable' | 'poor';
    issues: string[];
    suggestions: string[];
  } {
    // En una implementación real, usar análisis de imagen
    const mockQuality = ['excellent', 'good', 'acceptable', 'poor'][Math.floor(Math.random() * 4)] as any;
    
    const issues = [];
    const suggestions = [];
    
    switch (mockQuality) {
      case 'poor':
        issues.push('Iluminación insuficiente', 'Imagen borrosa');
        suggestions.push('Mejora la iluminación', 'Mantén el dispositivo estable');
        break;
      case 'acceptable':
        issues.push('Iluminación mejorable');
        suggestions.push('Busca luz natural si es posible');
        break;
      case 'good':
        suggestions.push('Buena calidad - puedes continuar');
        break;
      case 'excellent':
        suggestions.push('Excelente calidad de imagen');
        break;
    }

    return { quality: mockQuality, issues, suggestions };
  }

  // Guías contextuales para captura
  static getCaptureGuide(zone: string): {
    title: string;
    instructions: string[];
    tips: string[];
  } {
    const guides = {
      frontal: {
        title: 'Vista Frontal',
        instructions: [
          'Peina el cabello hacia atrás',
          'Asegúrate de que se vea toda la frente',
          'Mantén la cabeza recta',
        ],
        tips: [
          'Usa luz natural si es posible',
          'Evita sombras en el rostro',
        ],
      },
      coronilla: {
        title: 'Vista Superior (Coronilla)',
        instructions: [
          'Separa el cabello en el centro',
          'Enfoca la zona de la coronilla',
          'Captura desde arriba',
        ],
        tips: [
          'Pide al cliente que incline ligeramente la cabeza',
          'Asegúrate de ver las raíces claramente',
        ],
      },
      nuca: {
        title: 'Vista Posterior (Nuca)',
        instructions: [
          'Levanta el cabello si es necesario',
          'Enfoca la línea del cabello posterior',
          'Captura toda la zona de la nuca',
        ],
        tips: [
          'Usa un espejo si es necesario',
          'Asegúrate de buena iluminación posterior',
        ],
      },
    };

    return guides[zone as keyof typeof guides] || guides.frontal;
  }

  // Estimación de tiempo de proceso
  static estimateProcessingTime(imageCount: number, analysisType: string): number {
    const baseTime = {
      diagnosis: 30, // segundos
      colorAnalysis: 20,
      formulation: 45,
    };

    const timePerImage = 5; // segundos adicionales por imagen
    const base = baseTime[analysisType as keyof typeof baseTime] || 30;
    
    return base + (imageCount * timePerImage);
  }

  // Mensajes de progreso dinámicos
  static getProgressMessage(phase: string, progress: number): string {
    const messages = {
      diagnosis: [
        'Analizando nivel natural del cabello...',
        'Detectando subtonos y reflejos...',
        'Evaluando porosidad y condición...',
        'Generando diagnóstico completo...',
      ],
      colorAnalysis: [
        'Analizando imágenes de referencia...',
        'Identificando color objetivo...',
        'Calculando viabilidad...',
      ],
      formulation: [
        'Evaluando compatibilidad de productos...',
        'Calculando proporciones exactas...',
        'Generando instrucciones detalladas...',
        'Validando fórmula final...',
      ],
    };

    const phaseMessages = messages[phase as keyof typeof messages] || ['Procesando...'];
    const messageIndex = Math.floor((progress / 100) * phaseMessages.length);
    
    return phaseMessages[Math.min(messageIndex, phaseMessages.length - 1)];
  }
}

export default { CostOptimizer, UXOptimizer };
